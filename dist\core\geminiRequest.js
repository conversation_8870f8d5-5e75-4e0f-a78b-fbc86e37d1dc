/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { getResponseText, getFunctionCalls } from '../utils/generateContentResponseUtilities.js';
import { metrics } from '../telemetry/metrics.js';
/**
 * Makes a request to the Gemini API with proper formatting
 */
export async function makeGeminiRequest(client, userMessage, options = {}) {
    const { systemPrompt, tools, temperature, maxTokens } = options;
    // Build the request
    const contents = [];
    // Add system prompt if provided
    if (systemPrompt) {
        contents.push({ parts: [{ text: systemPrompt }], role: 'user' });
    }
    // Add user message
    contents.push({ parts: [{ text: userMessage }], role: 'user' });
    const request = {
        model: client.getModelName(),
        contents,
        config: {
            tools: tools ? [{ functionDeclarations: tools }] : undefined,
            temperature: temperature || 0.7,
            maxOutputTokens: maxTokens || 8192,
        },
    };
    try {
        const response = await client.generateContent(request);
        const text = getResponseText(response);
        const functionCalls = getFunctionCalls(response);
        return {
            text,
            functionCalls,
            usage: {
                promptTokens: response.usageMetadata?.promptTokenCount,
                completionTokens: response.usageMetadata?.candidatesTokenCount,
                totalTokens: response.usageMetadata?.totalTokenCount,
            },
        };
    }
    catch (error) {
        metrics.recordError('gemini_request_error', error instanceof Error ? error.message : String(error), { userMessage: userMessage.substring(0, 100) });
        throw error;
    }
}
/**
 * Makes a streaming request to the Gemini API
 */
export async function* makeGeminiStreamRequest(client, userMessage, options = {}) {
    const { systemPrompt, tools, temperature, maxTokens } = options;
    // Build the request
    const contents = [];
    // Add system prompt if provided
    if (systemPrompt) {
        contents.push({ parts: [{ text: systemPrompt }], role: 'user' });
    }
    // Add user message
    contents.push({ parts: [{ text: userMessage }], role: 'user' });
    const request = {
        model: client.getModelName(),
        contents,
        config: {
            tools: tools ? [{ functionDeclarations: tools }] : undefined,
            temperature: temperature || 0.7,
            maxOutputTokens: maxTokens || 8192,
        },
    };
    try {
        const stream = await client.generateContentStream(request);
        let fullText = '';
        let functionCalls = [];
        let usage = {};
        for await (const chunk of stream) {
            const chunkText = getResponseText(chunk);
            const chunkFunctionCalls = getFunctionCalls(chunk);
            if (chunkText) {
                fullText += chunkText;
                yield chunkText;
            }
            if (chunkFunctionCalls.length > 0) {
                functionCalls.push(...chunkFunctionCalls);
            }
            if (chunk.usageMetadata) {
                usage = {
                    promptTokens: chunk.usageMetadata.promptTokenCount,
                    completionTokens: chunk.usageMetadata.candidatesTokenCount,
                    totalTokens: chunk.usageMetadata.totalTokenCount,
                };
            }
        }
        return {
            text: fullText,
            functionCalls,
            usage,
        };
    }
    catch (error) {
        metrics.recordError('gemini_stream_error', error instanceof Error ? error.message : String(error), { userMessage: userMessage.substring(0, 100) });
        throw error;
    }
}
/**
 * Builds a conversation request with history
 */
export function buildConversationRequest(messages, options = {}, modelName = 'gemini-1.5-pro') {
    const { tools, temperature, maxTokens } = options;
    const contents = [];
    for (const message of messages) {
        if (message.role === 'system') {
            // System messages are typically added as the first user message
            contents.push({ parts: [{ text: `System: ${message.content}` }], role: 'user' });
        }
        else if (message.role === 'user') {
            contents.push({ parts: [{ text: message.content }], role: 'user' });
        }
        else if (message.role === 'assistant') {
            contents.push({ parts: [{ text: message.content }], role: 'model' });
        }
    }
    return {
        model: modelName,
        contents,
        config: {
            tools: tools ? [{ functionDeclarations: tools }] : undefined,
            temperature: temperature || 0.7,
            maxOutputTokens: maxTokens || 8192,
        },
    };
}
//# sourceMappingURL=geminiRequest.js.map