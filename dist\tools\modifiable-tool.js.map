{"version": 3, "file": "modifiable-tool.js", "sourceRoot": "", "sources": ["../../src/tools/modifiable-tool.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAc,QAAQ,EAAE,MAAM,oBAAoB,CAAC;AAC1D,OAAO,EAAE,MAAM,IAAI,CAAC;AACpB,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,EAAE,MAAM,IAAI,CAAC;AACpB,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,EAAE,oBAAoB,EAAE,MAAM,kBAAkB,CAAC;AACxD,OAAO,EAAE,WAAW,EAAE,MAAM,oBAAoB,CAAC;AA6BjD,MAAM,UAAU,gBAAgB,CAC9B,IAAmB;IAEnB,OAAO,kBAAkB,IAAI,IAAI,CAAC;AACpC,CAAC;AAED,SAAS,wBAAwB,CAC/B,cAAsB,EACtB,eAAuB,EACvB,SAAiB;IAEjB,MAAM,OAAO,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC;IAC5B,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,8BAA8B,CAAC,CAAC;IAEnE,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;QAC5B,EAAE,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IAC7C,CAAC;IAED,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IACpC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;IAC/C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC7B,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAC3B,OAAO,EACP,qBAAqB,QAAQ,QAAQ,SAAS,GAAG,GAAG,EAAE,CACvD,CAAC;IACF,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAC3B,OAAO,EACP,qBAAqB,QAAQ,QAAQ,SAAS,GAAG,GAAG,EAAE,CACvD,CAAC;IAEF,EAAE,CAAC,aAAa,CAAC,WAAW,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;IACtD,EAAE,CAAC,aAAa,CAAC,WAAW,EAAE,eAAe,EAAE,MAAM,CAAC,CAAC;IAEvD,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;AACxD,CAAC;AAED,SAAS,gBAAgB,CACvB,UAAkB,EAClB,WAAmB,EACnB,cAA0B,EAC1B,aAAwC;IAExC,IAAI,UAAU,GAAG,EAAE,CAAC;IACpB,IAAI,UAAU,GAAG,EAAE,CAAC;IAEpB,IAAI,CAAC;QACH,UAAU,GAAG,EAAE,CAAC,YAAY,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;IACnD,CAAC;IAAC,OAAO,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ;YAAE,MAAM,GAAG,CAAC;QAC1D,UAAU,GAAG,EAAE,CAAC;IAClB,CAAC;IAED,IAAI,CAAC;QACH,UAAU,GAAG,EAAE,CAAC,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;IACpD,CAAC;IAAC,OAAO,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ;YAAE,MAAM,GAAG,CAAC;QAC1D,UAAU,GAAG,EAAE,CAAC;IAClB,CAAC;IAED,MAAM,aAAa,GAAG,aAAa,CAAC,mBAAmB,CACrD,UAAU,EACV,UAAU,EACV,cAAc,CACf,CAAC;IACF,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAClC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,EACxD,UAAU,EACV,UAAU,EACV,SAAS,EACT,UAAU,EACV,oBAAoB,CACrB,CAAC;IAEF,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,CAAC;AACxC,CAAC;AAED,SAAS,eAAe,CAAC,OAAe,EAAE,OAAe;IACvD,IAAI,CAAC;QACH,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IACzB,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,KAAK,CAAC,kCAAkC,OAAO,EAAE,CAAC,CAAC;IAC7D,CAAC;IAED,IAAI,CAAC;QACH,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IACzB,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,CAAC,KAAK,CAAC,kCAAkC,OAAO,EAAE,CAAC,CAAC;IAC7D,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,MAAM,CAAC,KAAK,UAAU,gBAAgB,CACpC,cAA0B,EAC1B,aAAwC,EACxC,UAAsB,EACtB,YAAyB;IAEzB,MAAM,cAAc,GAAG,MAAM,aAAa,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;IAC7E,MAAM,eAAe,GACnB,MAAM,aAAa,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;IAEzD,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,wBAAwB,CACnD,cAAc,EACd,eAAe,EACf,aAAa,CAAC,WAAW,CAAC,cAAc,CAAC,CAC1C,CAAC;IAEF,IAAI,CAAC;QACH,MAAM,QAAQ,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC;QACzD,MAAM,MAAM,GAAG,gBAAgB,CAC7B,OAAO,EACP,OAAO,EACP,cAAc,EACd,aAAa,CACd,CAAC;QAEF,OAAO,MAAM,CAAC;IAChB,CAAC;YAAS,CAAC;QACT,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IACpC,CAAC;AACH,CAAC"}