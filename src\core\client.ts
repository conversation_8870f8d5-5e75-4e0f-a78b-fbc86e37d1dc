/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { GoogleGenAI, GenerateContentParameters, GenerateContentResponse } from '@google/genai';
import { Config } from '../config/config.js';
import { metrics } from '../telemetry/metrics.js';
import { getErrorMessage } from '../utils/errors.js';

export class GeminiClient {
  private client: GoogleGenAI;
  private config: Config;

  constructor(config: Config) {
    this.config = config;

    const apiKey = config.getApiKey();
    if (!apiKey) {
      throw new Error('Google API key is required for Gemini client');
    }

    this.client = new GoogleGenAI({ apiKey });
  }

  /**
   * Generates content using the Gemini API
   */
  async generateContent(request: GenerateContentParameters): Promise<GenerateContentResponse> {
    const startTime = Date.now();
    let success = false;
    let tokensUsed = 0;

    try {
      const response = await this.client.models.generateContent(request);

      success = true;
      tokensUsed = response.usageMetadata?.totalTokenCount || 0;

      // Record metrics
      metrics.recordLLMRequest(
        'google',
        this.config.getModel(),
        tokensUsed,
        Date.now() - startTime,
        success
      );

      return response;
    } catch (error) {
      // Record error metrics
      metrics.recordLLMRequest(
        'google',
        this.config.getModel(),
        tokensUsed,
        Date.now() - startTime,
        success
      );

      metrics.recordError(
        'gemini_request_failed',
        getErrorMessage(error),
        { model: this.config.getModel() }
      );

      throw error;
    }
  }

  /**
   * Generates content with streaming
   */
  async generateContentStream(request: GenerateContentParameters): Promise<AsyncIterable<GenerateContentResponse>> {
    const startTime = Date.now();
    let success = false;
    let tokensUsed = 0;

    try {
      const stream = await this.client.models.generateContentStream(request);

      success = true;

      // Record metrics (approximate for streaming)
      metrics.recordLLMRequest(
        'google',
        this.config.getModel(),
        tokensUsed,
        Date.now() - startTime,
        success
      );

      return stream;
    } catch (error) {
      // Record error metrics
      metrics.recordLLMRequest(
        'google',
        this.config.getModel(),
        tokensUsed,
        Date.now() - startTime,
        success
      );

      metrics.recordError(
        'gemini_stream_failed',
        getErrorMessage(error),
        { model: this.config.getModel() }
      );

      throw error;
    }
  }

  /**
   * Gets the current model name
   */
  getModelName(): string {
    return this.config.getModel();
  }

  /**
   * Updates the model configuration
   */
  updateModel(modelName: string): void {
    this.config.updateParams({ model: modelName });
    // Model configuration is now handled per-request in the new API
  }
}
