/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import os from 'os';

export type EditorType = 'vscode' | 'vim' | 'nano' | 'emacs' | 'default';

export interface DiffOptions {
  leftTitle?: string;
  rightTitle?: string;
  editor?: EditorType;
}

/**
 * Opens a diff view between two files or content
 */
export async function openDiff(
  leftContent: string,
  rightContent: string,
  options: DiffOptions = {}
): Promise<void> {
  const { leftTitle = 'Original', rightTitle = 'Modified', editor = 'default' } = options;
  
  // Create temporary files
  const tempDir = os.tmpdir();
  const leftFile = path.join(tempDir, `${leftTitle.replace(/[^a-zA-Z0-9]/g, '_')}.tmp`);
  const rightFile = path.join(tempDir, `${rightTitle.replace(/[^a-zA-Z0-9]/g, '_')}.tmp`);
  
  try {
    // Write content to temporary files
    fs.writeFileSync(leftFile, leftContent);
    fs.writeFileSync(rightFile, rightContent);
    
    // Open diff based on editor preference
    await openDiffWithEditor(leftFile, rightFile, editor);
  } finally {
    // Clean up temporary files
    try {
      fs.unlinkSync(leftFile);
      fs.unlinkSync(rightFile);
    } catch (error) {
      // Ignore cleanup errors
    }
  }
}

/**
 * Opens diff with the specified editor
 */
async function openDiffWithEditor(
  leftFile: string,
  rightFile: string,
  editor: EditorType
): Promise<void> {
  return new Promise((resolve, reject) => {
    let command: string;
    let args: string[];
    
    switch (editor) {
      case 'vscode':
        command = 'code';
        args = ['--diff', leftFile, rightFile];
        break;
      case 'vim':
        command = 'vim';
        args = ['-d', leftFile, rightFile];
        break;
      case 'nano':
        // Nano doesn't have built-in diff, so we'll use diff command
        command = 'diff';
        args = ['-u', leftFile, rightFile];
        break;
      case 'emacs':
        command = 'emacs';
        args = ['--eval', `(ediff-files "${leftFile}" "${rightFile}")`];
        break;
      default:
        // Use system default diff command
        command = 'diff';
        args = ['-u', leftFile, rightFile];
        break;
    }
    
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
    });
    
    child.on('close', () => {
      resolve();
    });
    
    child.on('error', (error) => {
      reject(error);
    });
  });
}

/**
 * Detects the available editor on the system
 */
export function detectEditor(): EditorType {
  const editors: Array<{ command: string; type: EditorType }> = [
    { command: 'code', type: 'vscode' },
    { command: 'vim', type: 'vim' },
    { command: 'nano', type: 'nano' },
    { command: 'emacs', type: 'emacs' },
  ];
  
  for (const { command, type } of editors) {
    try {
      // Try to run the command with --version to see if it exists
      const { execSync } = require('child_process');
      execSync(`${command} --version`, { stdio: 'ignore' });
      return type;
    } catch {
      // Command not found, try next
    }
  }
  
  return 'default';
}
