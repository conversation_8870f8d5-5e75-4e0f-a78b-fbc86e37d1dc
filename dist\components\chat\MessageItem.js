import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Box, Text } from 'ink';
export const MessageItem = ({ message, themeManager, showTimestamp = false, }) => {
    const formatTimestamp = (timestamp) => {
        return new Date(timestamp).toLocaleTimeString();
    };
    const getRoleDisplay = () => {
        switch (message.role) {
            case 'user':
                return themeManager.primary('You: ');
            case 'assistant':
                return themeManager.secondary('Arien: ');
            case 'system':
                return themeManager.muted('System: ');
            default:
                return themeManager.muted(`${message.role}: `);
        }
    };
    const getMessageContent = () => {
        if (message.error) {
            return themeManager.error(message.content);
        }
        if (message.role === 'system') {
            return themeManager.muted(message.content);
        }
        return message.content;
    };
    const renderStreamingCursor = () => {
        if (message.isStreaming) {
            return _jsx(Text, { children: themeManager.accent('▋') });
        }
        return null;
    };
    return (_jsxs(Box, { flexDirection: "column", marginBottom: 1, children: [_jsxs(Box, { children: [getRoleDisplay(), _jsx(Text, { children: getMessageContent() }), renderStreamingCursor()] }), showTimestamp && (_jsx(Box, { marginLeft: 2, children: _jsx(Text, { children: themeManager.muted(`[${formatTimestamp(message.timestamp)}]`) }) })), message.error && (_jsx(Box, { marginLeft: 2, children: _jsx(Text, { children: themeManager.error(`Error: ${message.error}`) }) }))] }));
};
//# sourceMappingURL=MessageItem.js.map