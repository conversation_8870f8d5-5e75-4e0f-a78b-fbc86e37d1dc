/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import OpenAI from 'openai';
import { BaseLLMProvider } from './base.js';
import { metrics } from '../telemetry/metrics.js';
import { getErrorMessage } from '../utils/errors.js';
export class OpenAIProvider extends BaseLLMProvider {
    client;
    constructor(config) {
        super('OpenAI', config);
        this.validateConfig();
        this.client = new OpenAI({
            apiKey: config.apiKey,
            baseURL: config.baseUrl,
        });
    }
    async generateResponse(message, options = {}) {
        const startTime = Date.now();
        let success = false;
        let tokensUsed = 0;
        try {
            const messages = [];
            if (options.systemPrompt) {
                messages.push({
                    role: 'system',
                    content: options.systemPrompt,
                });
            }
            messages.push({
                role: 'user',
                content: message,
            });
            const completion = await this.client.chat.completions.create({
                model: this.config.model,
                messages,
                temperature: options.temperature || this.config.temperature || 0.7,
                max_tokens: options.maxTokens || this.config.maxTokens || 4096,
                tools: options.tools ? this.convertTools(options.tools) : undefined,
            });
            success = true;
            tokensUsed = completion.usage?.total_tokens || 0;
            // Record metrics
            metrics.recordLLMRequest('openai', this.config.model, tokensUsed, Date.now() - startTime, success);
            return this.convertResponse(completion);
        }
        catch (error) {
            // Record error metrics
            metrics.recordLLMRequest('openai', this.config.model, tokensUsed, Date.now() - startTime, success);
            metrics.recordError('openai_request_failed', getErrorMessage(error), { model: this.config.model });
            throw error;
        }
    }
    async generateConversationResponse(messages, options = {}) {
        const startTime = Date.now();
        let success = false;
        let tokensUsed = 0;
        try {
            const openaiMessages = [];
            if (options.systemPrompt) {
                openaiMessages.push({
                    role: 'system',
                    content: options.systemPrompt,
                });
            }
            for (const msg of messages) {
                openaiMessages.push({
                    role: msg.role === 'assistant' ? 'assistant' : 'user',
                    content: msg.content,
                });
            }
            const completion = await this.client.chat.completions.create({
                model: this.config.model,
                messages: openaiMessages,
                temperature: options.temperature || this.config.temperature || 0.7,
                max_tokens: options.maxTokens || this.config.maxTokens || 4096,
                tools: options.tools ? this.convertTools(options.tools) : undefined,
            });
            success = true;
            tokensUsed = completion.usage?.total_tokens || 0;
            // Record metrics
            metrics.recordLLMRequest('openai', this.config.model, tokensUsed, Date.now() - startTime, success);
            return this.convertResponse(completion);
        }
        catch (error) {
            // Record error metrics
            metrics.recordLLMRequest('openai', this.config.model, tokensUsed, Date.now() - startTime, success);
            metrics.recordError('openai_conversation_failed', getErrorMessage(error), { model: this.config.model });
            throw error;
        }
    }
    async *generateStreamingResponse(message, options = {}) {
        const startTime = Date.now();
        let success = false;
        let tokensUsed = 0;
        try {
            const messages = [];
            if (options.systemPrompt) {
                messages.push({
                    role: 'system',
                    content: options.systemPrompt,
                });
            }
            messages.push({
                role: 'user',
                content: message,
            });
            const stream = await this.client.chat.completions.create({
                model: this.config.model,
                messages,
                temperature: options.temperature || this.config.temperature || 0.7,
                max_tokens: options.maxTokens || this.config.maxTokens || 4096,
                tools: options.tools ? this.convertTools(options.tools) : undefined,
                stream: true,
            });
            let fullText = '';
            let functionCalls = [];
            for await (const chunk of stream) {
                const delta = chunk.choices[0]?.delta;
                if (delta?.content) {
                    fullText += delta.content;
                    yield delta.content;
                }
                if (delta?.tool_calls) {
                    for (const toolCall of delta.tool_calls) {
                        if (toolCall.function?.name && toolCall.function?.arguments) {
                            try {
                                const args = JSON.parse(toolCall.function.arguments);
                                functionCalls.push({
                                    name: toolCall.function.name,
                                    args,
                                });
                            }
                            catch {
                                // Ignore malformed function calls
                            }
                        }
                    }
                }
            }
            success = true;
            // Record metrics (approximate for streaming)
            metrics.recordLLMRequest('openai', this.config.model, tokensUsed, Date.now() - startTime, success);
            return {
                text: fullText,
                functionCalls,
                usage: {
                    totalTokens: tokensUsed,
                },
            };
        }
        catch (error) {
            // Record error metrics
            metrics.recordLLMRequest('openai', this.config.model, tokensUsed, Date.now() - startTime, success);
            metrics.recordError('openai_stream_failed', getErrorMessage(error), { model: this.config.model });
            throw error;
        }
    }
    getAvailableModels() {
        return [
            'gpt-4o',
            'gpt-4o-mini',
            'gpt-4-turbo',
            'gpt-4',
            'gpt-3.5-turbo',
            'gpt-3.5-turbo-16k',
        ];
    }
    convertTools(tools) {
        return tools.map(tool => ({
            type: 'function',
            function: {
                name: tool.name || '',
                description: tool.description || '',
                parameters: tool.parameters || {},
            },
        }));
    }
    convertResponse(completion) {
        const choice = completion.choices[0];
        const message = choice?.message;
        const functionCalls = [];
        if (message?.tool_calls) {
            for (const toolCall of message.tool_calls) {
                if (toolCall.function?.name && toolCall.function?.arguments) {
                    try {
                        const args = JSON.parse(toolCall.function.arguments);
                        functionCalls.push({
                            name: toolCall.function.name,
                            args,
                        });
                    }
                    catch {
                        // Ignore malformed function calls
                    }
                }
            }
        }
        return {
            text: message?.content || '',
            functionCalls,
            usage: {
                promptTokens: completion.usage?.prompt_tokens,
                completionTokens: completion.usage?.completion_tokens,
                totalTokens: completion.usage?.total_tokens,
            },
            finishReason: choice?.finish_reason || undefined,
        };
    }
}
//# sourceMappingURL=openai.js.map