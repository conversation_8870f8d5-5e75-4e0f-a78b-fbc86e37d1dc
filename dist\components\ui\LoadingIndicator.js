import { jsx as _jsx } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState, useEffect } from 'react';
import { Text } from 'ink';
export const LoadingIndicator = ({ themeManager, text = 'Loading', type = 'spinner', }) => {
    const [frame, setFrame] = useState(0);
    useEffect(() => {
        const interval = setInterval(() => {
            setFrame(prev => prev + 1);
        }, type === 'pulse' ? 1000 : 200);
        return () => clearInterval(interval);
    }, [type]);
    const renderIndicator = () => {
        const theme = themeManager.getCurrentTheme();
        switch (type) {
            case 'dots':
                const dots = '.'.repeat((frame % 4));
                return `${text}${dots}`;
            case 'spinner':
                const spinnerFrames = theme.symbols.loading;
                const spinnerChar = spinnerFrames[frame % spinnerFrames.length];
                return `${spinnerChar} ${text}`;
            case 'pulse':
                const isPulse = frame % 2 === 0;
                return isPulse ? themeManager.primary(text) : themeManager.muted(text);
            default:
                return text;
        }
    };
    return _jsx(Text, { children: renderIndicator() });
};
//# sourceMappingURL=LoadingIndicator.js.map