import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { Component } from 'react';
import { Box, Text } from 'ink';
export class ErrorBoundary extends Component {
    constructor(props) {
        super(props);
        this.state = { hasError: false };
    }
    static getDerivedStateFromError(error) {
        return { hasError: true, error };
    }
    componentDidCatch(error, errorInfo) {
        console.error('Error caught by boundary:', error, errorInfo);
    }
    render() {
        if (this.state.hasError) {
            if (this.props.fallback) {
                return this.props.fallback;
            }
            return (_jsxs(Box, { flexDirection: "column", borderStyle: "round", borderColor: "red", padding: 1, children: [_jsx(Text, { children: this.props.themeManager.error('🚨 Application Error') }), _jsx(Text, {}), _jsx(Text, { children: this.props.themeManager.muted('An unexpected error occurred:') }), _jsx(Text, { children: this.props.themeManager.error(this.state.error?.message || 'Unknown error') }), _jsx(Text, {}), _jsx(Text, { children: this.props.themeManager.muted('Please restart the application.') })] }));
        }
        return this.props.children;
    }
}
//# sourceMappingURL=ErrorBoundary.js.map