/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { useState, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';
import { ThemeManager } from '../../themes/themes.js';
import { CredentialsManager } from '../../auth/credentials.js';
import { ProviderManager, ProviderType } from '../../providers/manager.js';
import { Header } from '../ui/Header.js';
import { LoadingIndicator } from '../ui/LoadingIndicator.js';

interface AuthScreenProps {
  themeManager: ThemeManager;
  credentialsManager: CredentialsManager;
  providerManager: ProviderManager;
  onAuthComplete: () => void;
}

type AuthStep = 'provider' | 'model' | 'apikey' | 'testing';

export const AuthScreen: React.FC<AuthScreenProps> = ({
  themeManager,
  credentialsManager,
  providerManager,
  onAuthComplete,
}) => {
  const [step, setStep] = useState<AuthStep>('provider');
  const [selectedProvider, setSelectedProvider] = useState<ProviderType | null>(null);
  const [selectedModel, setSelectedModel] = useState<string>('');
  const [apiKey, setApiKey] = useState<string>('');
  const [input, setInput] = useState<string>('');
  const [providerIndex, setProviderIndex] = useState(0);
  const [modelIndex, setModelIndex] = useState(0);
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [testResult, setTestResult] = useState<string | null>(null);

  const providers = providerManager.getAvailableProviders();
  const models = selectedProvider ? providerManager.getModelsForProvider(selectedProvider) : [];

  // Check if we already have configured providers
  useEffect(() => {
    const configuredProviders = credentialsManager.getConfiguredProviders();
    if (configuredProviders.length > 0) {
      const defaultProvider = credentialsManager.getDefaultProvider() || configuredProviders[0];
      const config = credentialsManager.getProviderConfig(defaultProvider);
      
      if (config.apiKey && config.model) {
        // Auto-proceed if we have valid configuration
        onAuthComplete();
        return;
      }
    }
  }, [credentialsManager, onAuthComplete]);

  useInput((inputChar, key) => {
    if (step === 'provider') {
      if (key.upArrow && providerIndex > 0) {
        setProviderIndex(providerIndex - 1);
      } else if (key.downArrow && providerIndex < providers.length - 1) {
        setProviderIndex(providerIndex + 1);
      } else if (key.return) {
        setSelectedProvider(providers[providerIndex].type);
        setStep('model');
        setModelIndex(0);
      }
    } else if (step === 'model') {
      if (key.upArrow && modelIndex > 0) {
        setModelIndex(modelIndex - 1);
      } else if (key.downArrow && modelIndex < models.length - 1) {
        setModelIndex(modelIndex + 1);
      } else if (key.return) {
        setSelectedModel(models[modelIndex]);
        setStep('apikey');
        setInput('');
      } else if (key.escape) {
        setStep('provider');
        setSelectedProvider(null);
      }
    } else if (step === 'apikey') {
      if (key.return && input.trim()) {
        setApiKey(input.trim());
        setStep('testing');
        testConnection();
      } else if (key.escape) {
        setStep('model');
        setInput('');
      } else if (key.backspace || key.delete) {
        setInput(input.slice(0, -1));
      } else if (inputChar && inputChar.length === 1 && !key.ctrl && !key.meta) {
        // Filter out non-printable characters
        const charCode = inputChar.charCodeAt(0);
        if (charCode >= 32 && charCode <= 126) {
          setInput(input + inputChar);
        }
      }
    } else if (step === 'testing') {
      if (key.return && testResult) {
        if (testResult === 'success') {
          onAuthComplete();
        } else {
          setStep('apikey');
          setInput('');
          setTestResult(null);
        }
      } else if (key.escape) {
        setStep('apikey');
        setInput('');
        setTestResult(null);
      }
    }
  });

  const testConnection = async () => {
    if (!selectedProvider || !selectedModel || !apiKey) return;

    setIsTestingConnection(true);
    setTestResult(null);

    try {
      // Validate API key format
      if (!credentialsManager.validateApiKey(selectedProvider, apiKey)) {
        setTestResult('invalid_format');
        setIsTestingConnection(false);
        return;
      }

      // Create provider and test connection
      const providerId = providerManager.createProvider(selectedProvider, {
        apiKey,
        model: selectedModel,
      });

      const isConnected = await providerManager.testProvider(providerId);

      if (isConnected) {
        // Save credentials
        credentialsManager.setApiKey(selectedProvider, apiKey);
        credentialsManager.setModel(selectedProvider, selectedModel);
        credentialsManager.setDefaultProvider(selectedProvider);
        
        setTestResult('success');
      } else {
        setTestResult('failed');
        providerManager.removeProvider(providerId);
      }
    } catch (error) {
      setTestResult('error');
    } finally {
      setIsTestingConnection(false);
    }
  };

  const renderProviderSelection = () => (
    <Box flexDirection="column">
      <Text>{themeManager.secondary('Select an AI Provider:')}</Text>
      <Text></Text>
      {providers.map((provider, index) => (
        <Box key={provider.type} marginLeft={2}>
          <Text>
            {index === providerIndex ? themeManager.highlight(' > ') : '   '}
            {themeManager.primary(provider.name)}
            {themeManager.muted(` (${provider.models.length} models)`)}
          </Text>
        </Box>
      ))}
      <Text></Text>
      <Text>{themeManager.muted('Use ↑↓ to navigate, Enter to select')}</Text>
    </Box>
  );

  const renderModelSelection = () => (
    <Box flexDirection="column">
      <Text>{themeManager.secondary(`Select a model for ${providers.find(p => p.type === selectedProvider)?.name}:`)}</Text>
      <Text></Text>
      {models.map((model, index) => (
        <Box key={model} marginLeft={2}>
          <Text>
            {index === modelIndex ? themeManager.highlight(' > ') : '   '}
            {themeManager.primary(model)}
          </Text>
        </Box>
      ))}
      <Text></Text>
      <Text>{themeManager.muted('Use ↑↓ to navigate, Enter to select, Esc to go back')}</Text>
    </Box>
  );

  const renderApiKeyInput = () => (
    <Box flexDirection="column">
      <Text>{themeManager.secondary(`Enter API Key for ${providers.find(p => p.type === selectedProvider)?.name}:`)}</Text>
      <Text></Text>
      <Box marginLeft={2}>
        <Text>{themeManager.primary('API Key: ')}</Text>
        <Text>{themeManager.accent('*'.repeat(Math.min(input.length, 40)))}</Text>
      </Box>
      <Text></Text>
      <Text>{themeManager.muted('Type your API key and press Enter, Esc to go back')}</Text>
      {selectedProvider === 'google' && (
        <Text>{themeManager.info('Get your API key from: https://makersuite.google.com/app/apikey')}</Text>
      )}
      {selectedProvider === 'openai' && (
        <Text>{themeManager.info('Get your API key from: https://platform.openai.com/api-keys')}</Text>
      )}
      {selectedProvider === 'deepseek' && (
        <Text>{themeManager.info('Get your API key from: https://platform.deepseek.com/api-keys')}</Text>
      )}
      {selectedProvider === 'anthropic' && (
        <Text>{themeManager.info('Get your API key from: https://console.anthropic.com/account/keys')}</Text>
      )}
    </Box>
  );

  const renderTesting = () => (
    <Box flexDirection="column">
      <Text>{themeManager.secondary('Testing Connection...')}</Text>
      <Text></Text>
      {isTestingConnection && (
        <Box marginLeft={2}>
          <LoadingIndicator 
            themeManager={themeManager} 
            text={`Connecting to ${providers.find(p => p.type === selectedProvider)?.name}`}
            type="spinner"
          />
        </Box>
      )}
      {testResult === 'success' && (
        <Box flexDirection="column" marginLeft={2}>
          <Text>{themeManager.success('✅ Connection successful!')}</Text>
          <Text>{themeManager.muted('Press Enter to continue')}</Text>
        </Box>
      )}
      {testResult === 'failed' && (
        <Box flexDirection="column" marginLeft={2}>
          <Text>{themeManager.error('❌ Connection failed')}</Text>
          <Text>{themeManager.muted('Please check your API key and try again')}</Text>
          <Text>{themeManager.muted('Press Enter to retry, Esc to go back')}</Text>
        </Box>
      )}
      {testResult === 'invalid_format' && (
        <Box flexDirection="column" marginLeft={2}>
          <Text>{themeManager.error('❌ Invalid API key format')}</Text>
          <Text>{themeManager.muted('Please check your API key format and try again')}</Text>
          <Text>{themeManager.muted('Press Enter to retry, Esc to go back')}</Text>
        </Box>
      )}
      {testResult === 'error' && (
        <Box flexDirection="column" marginLeft={2}>
          <Text>{themeManager.error('❌ Connection error')}</Text>
          <Text>{themeManager.muted('An error occurred while testing the connection')}</Text>
          <Text>{themeManager.muted('Press Enter to retry, Esc to go back')}</Text>
        </Box>
      )}
    </Box>
  );

  return (
    <Box flexDirection="column" padding={1}>
      <Header
        themeManager={themeManager}
        title="🤖 Arien AI CLI - Authentication"
        subtitle="Configure your AI provider to get started"
      />
      {step === 'provider' && renderProviderSelection()}
      {step === 'model' && renderModelSelection()}
      {step === 'apikey' && renderApiKeyInput()}
      {step === 'testing' && renderTesting()}
    </Box>
  );
};
