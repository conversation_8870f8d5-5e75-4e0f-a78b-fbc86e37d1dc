/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import fs from 'fs';
import path from 'path';
import { findGitRoot, getTrackedFiles } from '../utils/gitUtils.js';
import { getErrorMessage } from '../utils/errors.js';
export class FileDiscoveryService {
    rootPath;
    gitRoot;
    gitIgnoreCache = new Map();
    trackedFilesCache = null;
    constructor(rootPath) {
        this.rootPath = path.resolve(rootPath);
        this.gitRoot = findGitRoot(this.rootPath);
    }
    /**
     * Filters files based on git ignore rules
     */
    filterFiles(relativePaths, options = {}) {
        const { respectGitIgnore = true, includeHidden = false } = options;
        if (!respectGitIgnore && includeHidden) {
            return relativePaths;
        }
        return relativePaths.filter(relativePath => {
            // Filter hidden files if not included
            if (!includeHidden && this.isHiddenFile(relativePath)) {
                return false;
            }
            // Filter git-ignored files if respectGitIgnore is true
            if (respectGitIgnore && this.shouldGitIgnoreFile(relativePath)) {
                return false;
            }
            return true;
        });
    }
    /**
     * Checks if a file should be git-ignored
     */
    shouldGitIgnoreFile(relativePath) {
        if (!this.gitRoot) {
            return false;
        }
        // Check cache first
        if (this.gitIgnoreCache.has(relativePath)) {
            return this.gitIgnoreCache.get(relativePath);
        }
        // Check if file is tracked (tracked files are never ignored)
        if (this.isTrackedFile(relativePath)) {
            this.gitIgnoreCache.set(relativePath, false);
            return false;
        }
        // Check git ignore rules
        try {
            // Use a simple heuristic for common ignore patterns
            const isIgnored = this.matchesGitIgnorePatterns(relativePath);
            this.gitIgnoreCache.set(relativePath, isIgnored);
            return isIgnored;
        }
        catch (error) {
            console.debug('Error checking git ignore:', getErrorMessage(error));
            this.gitIgnoreCache.set(relativePath, false);
            return false;
        }
    }
    /**
     * Checks if a file is tracked by git
     */
    isTrackedFile(relativePath) {
        if (!this.gitRoot) {
            return false;
        }
        if (!this.trackedFilesCache) {
            this.loadTrackedFiles();
        }
        return this.trackedFilesCache?.has(relativePath) || false;
    }
    /**
     * Loads tracked files from git
     */
    async loadTrackedFiles() {
        if (!this.gitRoot) {
            this.trackedFilesCache = new Set();
            return;
        }
        try {
            const trackedFiles = await getTrackedFiles(this.gitRoot);
            this.trackedFilesCache = new Set(trackedFiles);
        }
        catch (error) {
            console.debug('Failed to load tracked files:', getErrorMessage(error));
            this.trackedFilesCache = new Set();
        }
    }
    /**
     * Simple git ignore pattern matching
     */
    matchesGitIgnorePatterns(relativePath) {
        const commonIgnorePatterns = [
            'node_modules',
            '.git',
            '.vscode',
            '.idea',
            'dist',
            'build',
            'coverage',
            '__pycache__',
            '.DS_Store',
            '*.log',
            '*.tmp',
            '*.temp',
            '.env',
            '.env.local',
            '.env.development',
            '.env.production',
        ];
        const pathParts = relativePath.split(path.sep);
        for (const pattern of commonIgnorePatterns) {
            if (pattern.includes('*')) {
                // Simple wildcard matching
                const regex = new RegExp(pattern.replace(/\*/g, '.*'));
                if (regex.test(path.basename(relativePath))) {
                    return true;
                }
            }
            else {
                // Exact match for directory or file names
                if (pathParts.includes(pattern) || path.basename(relativePath) === pattern) {
                    return true;
                }
            }
        }
        return false;
    }
    /**
     * Checks if a file is hidden (starts with .)
     */
    isHiddenFile(relativePath) {
        const parts = relativePath.split(path.sep);
        return parts.some(part => part.startsWith('.') && part !== '.' && part !== '..');
    }
    /**
     * Discovers files in a directory
     */
    async discoverFiles(searchPath = this.rootPath, options = {}) {
        const { maxDepth = 10 } = options;
        const files = [];
        try {
            await this.discoverFilesRecursive(searchPath, files, 0, maxDepth);
            // Convert to relative paths
            const relativePaths = files.map(file => path.relative(this.rootPath, file));
            // Apply filters
            return this.filterFiles(relativePaths, options);
        }
        catch (error) {
            console.error('Error discovering files:', getErrorMessage(error));
            return [];
        }
    }
    /**
     * Recursively discovers files
     */
    async discoverFilesRecursive(dirPath, files, currentDepth, maxDepth) {
        if (currentDepth >= maxDepth) {
            return;
        }
        try {
            const entries = await fs.promises.readdir(dirPath, { withFileTypes: true });
            for (const entry of entries) {
                const fullPath = path.join(dirPath, entry.name);
                if (entry.isFile()) {
                    files.push(fullPath);
                }
                else if (entry.isDirectory()) {
                    await this.discoverFilesRecursive(fullPath, files, currentDepth + 1, maxDepth);
                }
            }
        }
        catch (error) {
            console.debug(`Error reading directory ${dirPath}:`, getErrorMessage(error));
        }
    }
    /**
     * Gets file statistics
     */
    async getFileStats(filePath) {
        try {
            const absolutePath = path.resolve(this.rootPath, filePath);
            return await fs.promises.stat(absolutePath);
        }
        catch {
            return null;
        }
    }
    /**
     * Gets git ignore patterns
     */
    getGitIgnorePatterns() {
        // Return common git ignore patterns
        return [
            'node_modules/**',
            '.git/**',
            'dist/**',
            'build/**',
            '*.log',
            '.env*',
            '.DS_Store',
            'Thumbs.db',
        ];
    }
    /**
     * Clears internal caches
     */
    clearCache() {
        this.gitIgnoreCache.clear();
        this.trackedFilesCache = null;
    }
}
//# sourceMappingURL=fileDiscoveryService.js.map