/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

export interface MetricEvent {
  name: string;
  timestamp: number;
  properties?: Record<string, unknown>;
  measurements?: Record<string, number>;
}

export interface SessionMetrics {
  sessionId: string;
  startTime: number;
  endTime?: number;
  events: MetricEvent[];
  totalTokensUsed: number;
  totalRequests: number;
  errors: number;
}

export class MetricsCollector {
  private static instance: MetricsCollector | null = null;
  private currentSession: SessionMetrics | null = null;
  private enabled: boolean = true;

  private constructor() {}

  static getInstance(): MetricsCollector {
    if (!MetricsCollector.instance) {
      MetricsCollector.instance = new MetricsCollector();
    }
    return MetricsCollector.instance;
  }

  /**
   * Starts a new metrics session
   */
  startSession(sessionId: string): void {
    if (!this.enabled) return;

    this.currentSession = {
      sessionId,
      startTime: Date.now(),
      events: [],
      totalTokensUsed: 0,
      totalRequests: 0,
      errors: 0,
    };

    this.recordEvent('session_started', {
      sessionId,
    });
  }

  /**
   * Ends the current session
   */
  endSession(): void {
    if (!this.enabled || !this.currentSession) return;

    this.currentSession.endTime = Date.now();
    
    this.recordEvent('session_ended', {
      sessionId: this.currentSession.sessionId,
      duration: this.currentSession.endTime - this.currentSession.startTime,
      totalTokensUsed: this.currentSession.totalTokensUsed,
      totalRequests: this.currentSession.totalRequests,
      errors: this.currentSession.errors,
    });

    // In a full implementation, this would send metrics to a telemetry service
    this.currentSession = null;
  }

  /**
   * Records a metric event
   */
  recordEvent(
    name: string,
    properties?: Record<string, unknown>,
    measurements?: Record<string, number>
  ): void {
    if (!this.enabled) return;

    const event: MetricEvent = {
      name,
      timestamp: Date.now(),
      properties,
      measurements,
    };

    if (this.currentSession) {
      this.currentSession.events.push(event);
    }

    // Log to console in debug mode
    if (process.env.DEBUG_METRICS) {
      console.debug('Metric event:', event);
    }
  }

  /**
   * Records LLM request metrics
   */
  recordLLMRequest(
    provider: string,
    model: string,
    tokensUsed: number,
    duration: number,
    success: boolean
  ): void {
    if (!this.enabled) return;

    if (this.currentSession) {
      this.currentSession.totalRequests++;
      this.currentSession.totalTokensUsed += tokensUsed;
      if (!success) {
        this.currentSession.errors++;
      }
    }

    this.recordEvent('llm_request', {
      provider,
      model,
      success,
    }, {
      tokensUsed,
      duration,
    });
  }

  /**
   * Records tool execution metrics
   */
  recordToolExecution(
    toolName: string,
    duration: number,
    success: boolean,
    errorMessage?: string
  ): void {
    if (!this.enabled) return;

    this.recordEvent('tool_execution', {
      toolName,
      success,
      errorMessage,
    }, {
      duration,
    });
  }

  /**
   * Records file operation metrics
   */
  recordFileOperation(
    operation: string,
    fileCount: number,
    duration: number,
    success: boolean
  ): void {
    if (!this.enabled) return;

    this.recordEvent('file_operation', {
      operation,
      success,
    }, {
      fileCount,
      duration,
    });
  }

  /**
   * Records error metrics
   */
  recordError(
    errorType: string,
    errorMessage: string,
    context?: Record<string, unknown>
  ): void {
    if (!this.enabled) return;

    if (this.currentSession) {
      this.currentSession.errors++;
    }

    this.recordEvent('error', {
      errorType,
      errorMessage,
      ...context,
    });
  }

  /**
   * Gets current session metrics
   */
  getCurrentSession(): SessionMetrics | null {
    return this.currentSession;
  }

  /**
   * Enables or disables metrics collection
   */
  setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }

  /**
   * Checks if metrics collection is enabled
   */
  isEnabled(): boolean {
    return this.enabled;
  }
}

// Export singleton instance
export const metrics = MetricsCollector.getInstance();

// Export types and helper functions
export enum FileOperation {
  READ = 'read',
  WRITE = 'write',
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  COPY = 'copy',
  MOVE = 'move',
}

export function recordFileOperationMetric(
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  config: any,
  operation: FileOperation,
  lines: number,
  mimetype: string,
  extension: string
): void {
  // Record the operation with additional metadata
  metrics.recordFileOperation(operation, lines, 0, true);

  // Record additional metadata as a separate event
  metrics.recordEvent('file_operation_details', {
    operation,
    mimetype,
    extension,
  }, {
    lines,
  });
}
