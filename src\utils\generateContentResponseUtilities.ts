/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { GenerateContentResponse } from '@google/genai';

/**
 * Extracts text response from Gemini API response
 */
export function getResponseText(response: GenerateContentResponse): string {
  if (!response.candidates || response.candidates.length === 0) {
    return '';
  }

  const candidate = response.candidates[0];
  if (!candidate?.content?.parts) {
    return '';
  }

  return candidate.content.parts
    .map(part => part.text || '')
    .join('')
    .trim();
}

/**
 * Checks if response was blocked by safety filters
 */
export function isResponseBlocked(response: GenerateContentResponse): boolean {
  if (!response.candidates || response.candidates.length === 0) {
    return false;
  }

  const candidate = response.candidates[0];
  return candidate?.finishReason === 'SAFETY' ||
         candidate?.finishReason === 'BLOCKLIST' ||
         candidate?.finishReason === 'PROHIBITED_CONTENT';
}

/**
 * Gets the finish reason from response
 */
export function getFinishReason(response: GenerateContentResponse): string | undefined {
  if (!response.candidates || response.candidates.length === 0) {
    return undefined;
  }

  return response.candidates[0]?.finishReason;
}

/**
 * Extracts function calls from response
 */
export function getFunctionCalls(response: GenerateContentResponse): Array<{
  name: string;
  args: Record<string, unknown>;
}> {
  if (!response.candidates || response.candidates.length === 0) {
    return [];
  }

  const candidate = response.candidates[0];
  if (!candidate?.content?.parts) {
    return [];
  }

  const functionCalls: Array<{ name: string; args: Record<string, unknown> }> = [];

  for (const part of candidate.content.parts) {
    if (part.functionCall && part.functionCall.name) {
      functionCalls.push({
        name: part.functionCall.name,
        args: part.functionCall.args || {},
      });
    }
  }

  return functionCalls;
}

/**
 * Checks if response contains function calls
 */
export function hasFunctionCalls(response: GenerateContentResponse): boolean {
  return getFunctionCalls(response).length > 0;
}

/**
 * Gets usage metadata from response
 */
export function getUsageMetadata(response: GenerateContentResponse): {
  promptTokenCount?: number;
  candidatesTokenCount?: number;
  totalTokenCount?: number;
} {
  return {
    promptTokenCount: response.usageMetadata?.promptTokenCount,
    candidatesTokenCount: response.usageMetadata?.candidatesTokenCount,
    totalTokenCount: response.usageMetadata?.totalTokenCount,
  };
}
