import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Box, Text } from 'ink';
import { useInput } from '../../hooks/useInput.js';
export const ChatInput = ({ themeManager, onSendMessage, onClear, onHelp, onExit, disabled = false, placeholder = 'Type your message...', }) => {
    const { input, isDisabled } = useInput({
        onSubmit: onSendMessage,
        onClear,
        onHelp,
        onExit,
        disabled,
    });
    const renderPrompt = () => {
        if (isDisabled) {
            return themeManager.muted('> ');
        }
        return themeManager.primary('> ');
    };
    const renderInput = () => {
        if (input) {
            return input;
        }
        if (isDisabled) {
            return themeManager.muted('Please wait...');
        }
        return themeManager.muted(placeholder);
    };
    const renderCursor = () => {
        if (!isDisabled) {
            return _jsx(Text, { children: themeManager.accent('▋') });
        }
        return null;
    };
    return (_jsxs(Box, { borderStyle: "round", borderColor: isDisabled ? "gray" : "blue", padding: 1, children: [_jsx(Text, { children: renderPrompt() }), _jsx(Text, { children: renderInput() }), renderCursor()] }));
};
//# sourceMappingURL=ChatInput.js.map