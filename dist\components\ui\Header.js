import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Box, Text } from 'ink';
export const Header = ({ themeManager, providerManager, title = '🤖 Arien AI CLI', subtitle, showProviderInfo = false, showControls = false, }) => {
    const renderProviderInfo = () => {
        if (!showProviderInfo || !providerManager)
            return null;
        try {
            const provider = providerManager.getCurrentProvider();
            return (_jsx(Text, { children: themeManager.muted(`Provider: ${provider.getName()} | Model: ${provider.getModel()}`) }));
        }
        catch {
            return (_jsx(Text, { children: themeManager.error('No provider configured') }));
        }
    };
    const renderControls = () => {
        if (!showControls)
            return null;
        return (_jsx(Text, { children: themeManager.muted('Type your message and press Enter. Ctrl+H for help, Ctrl+L to clear, Ctrl+C to exit') }));
    };
    return (_jsxs(Box, { flexDirection: "column", borderStyle: "round", borderColor: "gray", padding: 1, marginBottom: 1, children: [_jsx(Text, { children: themeManager.primary(title) }), subtitle && _jsx(Text, { children: themeManager.muted(subtitle) }), renderProviderInfo(), renderControls()] }));
};
//# sourceMappingURL=Header.js.map