/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { GenerateContentParameters, GenerateContentResponse } from '@google/genai';
import { Config } from '../config/config.js';
export declare class GeminiClient {
    private client;
    private config;
    constructor(config: Config);
    /**
     * Generates content using the Gemini API
     */
    generateContent(request: GenerateContentParameters): Promise<GenerateContentResponse>;
    /**
     * Generates content with streaming
     */
    generateContentStream(request: GenerateContentParameters): Promise<AsyncIterable<GenerateContentResponse>>;
    /**
     * Gets the current model name
     */
    getModelName(): string;
    /**
     * Updates the model configuration
     */
    updateModel(modelName: string): void;
}
//# sourceMappingURL=client.d.ts.map