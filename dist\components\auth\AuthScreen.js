import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';
import { Header } from '../ui/Header.js';
import { LoadingIndicator } from '../ui/LoadingIndicator.js';
export const AuthScreen = ({ themeManager, credentialsManager, providerManager, onAuthComplete, }) => {
    const [step, setStep] = useState('provider');
    const [selectedProvider, setSelectedProvider] = useState(null);
    const [selectedModel, setSelectedModel] = useState('');
    const [apiKey, setApiKey] = useState('');
    const [input, setInput] = useState('');
    const [providerIndex, setProviderIndex] = useState(0);
    const [modelIndex, setModelIndex] = useState(0);
    const [isTestingConnection, setIsTestingConnection] = useState(false);
    const [testResult, setTestResult] = useState(null);
    const providers = providerManager.getAvailableProviders();
    const models = selectedProvider ? providerManager.getModelsForProvider(selectedProvider) : [];
    // Check if we already have configured providers
    useEffect(() => {
        const configuredProviders = credentialsManager.getConfiguredProviders();
        if (configuredProviders.length > 0) {
            const defaultProvider = credentialsManager.getDefaultProvider() || configuredProviders[0];
            const config = credentialsManager.getProviderConfig(defaultProvider);
            if (config.apiKey && config.model) {
                // Auto-proceed if we have valid configuration
                onAuthComplete();
                return;
            }
        }
    }, [credentialsManager, onAuthComplete]);
    useInput((inputChar, key) => {
        if (step === 'provider') {
            if (key.upArrow && providerIndex > 0) {
                setProviderIndex(providerIndex - 1);
            }
            else if (key.downArrow && providerIndex < providers.length - 1) {
                setProviderIndex(providerIndex + 1);
            }
            else if (key.return) {
                setSelectedProvider(providers[providerIndex].type);
                setStep('model');
                setModelIndex(0);
            }
        }
        else if (step === 'model') {
            if (key.upArrow && modelIndex > 0) {
                setModelIndex(modelIndex - 1);
            }
            else if (key.downArrow && modelIndex < models.length - 1) {
                setModelIndex(modelIndex + 1);
            }
            else if (key.return) {
                setSelectedModel(models[modelIndex]);
                setStep('apikey');
                setInput('');
            }
            else if (key.escape) {
                setStep('provider');
                setSelectedProvider(null);
            }
        }
        else if (step === 'apikey') {
            if (key.return && input.trim()) {
                setApiKey(input.trim());
                setStep('testing');
                testConnection();
            }
            else if (key.escape) {
                setStep('model');
                setInput('');
            }
            else if (key.backspace || key.delete) {
                setInput(input.slice(0, -1));
            }
            else if (inputChar && inputChar.length === 1 && !key.ctrl && !key.meta) {
                // Filter out non-printable characters
                const charCode = inputChar.charCodeAt(0);
                if (charCode >= 32 && charCode <= 126) {
                    setInput(input + inputChar);
                }
            }
        }
        else if (step === 'testing') {
            if (key.return && testResult) {
                if (testResult === 'success') {
                    onAuthComplete();
                }
                else {
                    setStep('apikey');
                    setInput('');
                    setTestResult(null);
                }
            }
            else if (key.escape) {
                setStep('apikey');
                setInput('');
                setTestResult(null);
            }
        }
    });
    const testConnection = async () => {
        if (!selectedProvider || !selectedModel || !apiKey)
            return;
        setIsTestingConnection(true);
        setTestResult(null);
        try {
            // Validate API key format
            if (!credentialsManager.validateApiKey(selectedProvider, apiKey)) {
                setTestResult('invalid_format');
                setIsTestingConnection(false);
                return;
            }
            // Create provider and test connection
            const providerId = providerManager.createProvider(selectedProvider, {
                apiKey,
                model: selectedModel,
            });
            const isConnected = await providerManager.testProvider(providerId);
            if (isConnected) {
                // Save credentials
                credentialsManager.setApiKey(selectedProvider, apiKey);
                credentialsManager.setModel(selectedProvider, selectedModel);
                credentialsManager.setDefaultProvider(selectedProvider);
                setTestResult('success');
            }
            else {
                setTestResult('failed');
                providerManager.removeProvider(providerId);
            }
        }
        catch (error) {
            setTestResult('error');
        }
        finally {
            setIsTestingConnection(false);
        }
    };
    const renderProviderSelection = () => (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: themeManager.secondary('Select an AI Provider:') }), _jsx(Text, {}), providers.map((provider, index) => (_jsx(Box, { marginLeft: 2, children: _jsxs(Text, { children: [index === providerIndex ? themeManager.highlight(' > ') : '   ', themeManager.primary(provider.name), themeManager.muted(` (${provider.models.length} models)`)] }) }, provider.type))), _jsx(Text, {}), _jsx(Text, { children: themeManager.muted('Use ↑↓ to navigate, Enter to select') })] }));
    const renderModelSelection = () => (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: themeManager.secondary(`Select a model for ${providers.find(p => p.type === selectedProvider)?.name}:`) }), _jsx(Text, {}), models.map((model, index) => (_jsx(Box, { marginLeft: 2, children: _jsxs(Text, { children: [index === modelIndex ? themeManager.highlight(' > ') : '   ', themeManager.primary(model)] }) }, model))), _jsx(Text, {}), _jsx(Text, { children: themeManager.muted('Use ↑↓ to navigate, Enter to select, Esc to go back') })] }));
    const renderApiKeyInput = () => (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: themeManager.secondary(`Enter API Key for ${providers.find(p => p.type === selectedProvider)?.name}:`) }), _jsx(Text, {}), _jsxs(Box, { marginLeft: 2, children: [_jsx(Text, { children: themeManager.primary('API Key: ') }), _jsx(Text, { children: themeManager.accent('*'.repeat(Math.min(input.length, 40))) })] }), _jsx(Text, {}), _jsx(Text, { children: themeManager.muted('Type your API key and press Enter, Esc to go back') }), selectedProvider === 'google' && (_jsx(Text, { children: themeManager.info('Get your API key from: https://makersuite.google.com/app/apikey') })), selectedProvider === 'openai' && (_jsx(Text, { children: themeManager.info('Get your API key from: https://platform.openai.com/api-keys') })), selectedProvider === 'deepseek' && (_jsx(Text, { children: themeManager.info('Get your API key from: https://platform.deepseek.com/api-keys') })), selectedProvider === 'anthropic' && (_jsx(Text, { children: themeManager.info('Get your API key from: https://console.anthropic.com/account/keys') }))] }));
    const renderTesting = () => (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: themeManager.secondary('Testing Connection...') }), _jsx(Text, {}), isTestingConnection && (_jsx(Box, { marginLeft: 2, children: _jsx(LoadingIndicator, { themeManager: themeManager, text: `Connecting to ${providers.find(p => p.type === selectedProvider)?.name}`, type: "spinner" }) })), testResult === 'success' && (_jsxs(Box, { flexDirection: "column", marginLeft: 2, children: [_jsx(Text, { children: themeManager.success('✅ Connection successful!') }), _jsx(Text, { children: themeManager.muted('Press Enter to continue') })] })), testResult === 'failed' && (_jsxs(Box, { flexDirection: "column", marginLeft: 2, children: [_jsx(Text, { children: themeManager.error('❌ Connection failed') }), _jsx(Text, { children: themeManager.muted('Please check your API key and try again') }), _jsx(Text, { children: themeManager.muted('Press Enter to retry, Esc to go back') })] })), testResult === 'invalid_format' && (_jsxs(Box, { flexDirection: "column", marginLeft: 2, children: [_jsx(Text, { children: themeManager.error('❌ Invalid API key format') }), _jsx(Text, { children: themeManager.muted('Please check your API key format and try again') }), _jsx(Text, { children: themeManager.muted('Press Enter to retry, Esc to go back') })] })), testResult === 'error' && (_jsxs(Box, { flexDirection: "column", marginLeft: 2, children: [_jsx(Text, { children: themeManager.error('❌ Connection error') }), _jsx(Text, { children: themeManager.muted('An error occurred while testing the connection') }), _jsx(Text, { children: themeManager.muted('Press Enter to retry, Esc to go back') })] }))] }));
    return (_jsxs(Box, { flexDirection: "column", padding: 1, children: [_jsx(Header, { themeManager: themeManager, title: "\uD83E\uDD16 Arien AI CLI - Authentication", subtitle: "Configure your AI provider to get started" }), step === 'provider' && renderProviderSelection(), step === 'model' && renderModelSelection(), step === 'apikey' && renderApiKeyInput(), step === 'testing' && renderTesting()] }));
};
//# sourceMappingURL=AuthScreen.js.map