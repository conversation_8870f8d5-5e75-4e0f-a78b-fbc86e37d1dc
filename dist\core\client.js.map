{"version": 3, "file": "client.js", "sourceRoot": "", "sources": ["../../src/core/client.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,WAAW,EAAsD,MAAM,eAAe,CAAC;AAEhG,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAErD,MAAM,OAAO,YAAY;IACf,MAAM,CAAc;IACpB,MAAM,CAAS;IAEvB,YAAY,MAAc;QACxB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;QAClC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,IAAI,WAAW,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,OAAkC;QACtD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAEnE,OAAO,GAAG,IAAI,CAAC;YACf,UAAU,GAAG,QAAQ,CAAC,aAAa,EAAE,eAAe,IAAI,CAAC,CAAC;YAE1D,iBAAiB;YACjB,OAAO,CAAC,gBAAgB,CACtB,QAAQ,EACR,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,EACtB,UAAU,EACV,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EACtB,OAAO,CACR,CAAC;YAEF,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,uBAAuB;YACvB,OAAO,CAAC,gBAAgB,CACtB,QAAQ,EACR,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,EACtB,UAAU,EACV,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EACtB,OAAO,CACR,CAAC;YAEF,OAAO,CAAC,WAAW,CACjB,uBAAuB,EACvB,eAAe,CAAC,KAAK,CAAC,EACtB,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE,CAClC,CAAC;YAEF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CAAC,OAAkC;QAC5D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;YAEvE,OAAO,GAAG,IAAI,CAAC;YAEf,6CAA6C;YAC7C,OAAO,CAAC,gBAAgB,CACtB,QAAQ,EACR,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,EACtB,UAAU,EACV,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EACtB,OAAO,CACR,CAAC;YAEF,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,uBAAuB;YACvB,OAAO,CAAC,gBAAgB,CACtB,QAAQ,EACR,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,EACtB,UAAU,EACV,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EACtB,OAAO,CACR,CAAC;YAEF,OAAO,CAAC,WAAW,CACjB,sBAAsB,EACtB,eAAe,CAAC,KAAK,CAAC,EACtB,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE,CAClC,CAAC;YAEF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,YAAY;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,SAAiB;QAC3B,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC;QAC/C,gEAAgE;IAClE,CAAC;CACF"}