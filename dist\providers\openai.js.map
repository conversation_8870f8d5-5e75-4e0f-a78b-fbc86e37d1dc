{"version": 3, "file": "openai.js", "sourceRoot": "", "sources": ["../../src/providers/openai.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,MAAM,MAAM,QAAQ,CAAC;AAE5B,OAAO,EAAE,eAAe,EAA8D,MAAM,WAAW,CAAC;AACxG,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAErD,MAAM,OAAO,cAAe,SAAQ,eAAe;IACzC,MAAM,CAAS;IAEvB,YAAY,MAAsB;QAChC,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACxB,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC;YACvB,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,OAAO,EAAE,MAAM,CAAC,OAAO;SACxB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,gBAAgB,CACpB,OAAe,EACf,UAA6B,EAAE;QAE/B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,IAAI,CAAC;YACH,MAAM,QAAQ,GAA6C,EAAE,CAAC;YAE9D,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;gBACzB,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,OAAO,CAAC,YAAY;iBAC9B,CAAC,CAAC;YACL,CAAC;YAED,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,OAAO;aACjB,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBAC3D,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;gBACxB,QAAQ;gBACR,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,GAAG;gBAClE,UAAU,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI;gBAC9D,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;aACpE,CAAC,CAAC;YAEH,OAAO,GAAG,IAAI,CAAC;YACf,UAAU,GAAG,UAAU,CAAC,KAAK,EAAE,YAAY,IAAI,CAAC,CAAC;YAEjD,iBAAiB;YACjB,OAAO,CAAC,gBAAgB,CACtB,QAAQ,EACR,IAAI,CAAC,MAAM,CAAC,KAAK,EACjB,UAAU,EACV,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EACtB,OAAO,CACR,CAAC;YAEF,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;QAC1C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,uBAAuB;YACvB,OAAO,CAAC,gBAAgB,CACtB,QAAQ,EACR,IAAI,CAAC,MAAM,CAAC,KAAK,EACjB,UAAU,EACV,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EACtB,OAAO,CACR,CAAC;YAEF,OAAO,CAAC,WAAW,CACjB,uBAAuB,EACvB,eAAe,CAAC,KAAK,CAAC,EACtB,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAC7B,CAAC;YAEF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,4BAA4B,CAChC,QAAsB,EACtB,UAA6B,EAAE;QAE/B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,IAAI,CAAC;YACH,MAAM,cAAc,GAA6C,EAAE,CAAC;YAEpE,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;gBACzB,cAAc,CAAC,IAAI,CAAC;oBAClB,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,OAAO,CAAC,YAAY;iBAC9B,CAAC,CAAC;YACL,CAAC;YAED,KAAK,MAAM,GAAG,IAAI,QAAQ,EAAE,CAAC;gBAC3B,cAAc,CAAC,IAAI,CAAC;oBAClB,IAAI,EAAE,GAAG,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM;oBACrD,OAAO,EAAE,GAAG,CAAC,OAAO;iBACrB,CAAC,CAAC;YACL,CAAC;YAED,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBAC3D,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;gBACxB,QAAQ,EAAE,cAAc;gBACxB,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,GAAG;gBAClE,UAAU,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI;gBAC9D,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;aACpE,CAAC,CAAC;YAEH,OAAO,GAAG,IAAI,CAAC;YACf,UAAU,GAAG,UAAU,CAAC,KAAK,EAAE,YAAY,IAAI,CAAC,CAAC;YAEjD,iBAAiB;YACjB,OAAO,CAAC,gBAAgB,CACtB,QAAQ,EACR,IAAI,CAAC,MAAM,CAAC,KAAK,EACjB,UAAU,EACV,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EACtB,OAAO,CACR,CAAC;YAEF,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;QAC1C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,uBAAuB;YACvB,OAAO,CAAC,gBAAgB,CACtB,QAAQ,EACR,IAAI,CAAC,MAAM,CAAC,KAAK,EACjB,UAAU,EACV,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EACtB,OAAO,CACR,CAAC;YAEF,OAAO,CAAC,WAAW,CACjB,4BAA4B,EAC5B,eAAe,CAAC,KAAK,CAAC,EACtB,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAC7B,CAAC;YAEF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAA,CAAE,yBAAyB,CAC9B,OAAe,EACf,UAA6B,EAAE;QAE/B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,IAAI,CAAC;YACH,MAAM,QAAQ,GAA6C,EAAE,CAAC;YAE9D,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;gBACzB,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,OAAO,CAAC,YAAY;iBAC9B,CAAC,CAAC;YACL,CAAC;YAED,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,OAAO;aACjB,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACvD,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;gBACxB,QAAQ;gBACR,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,GAAG;gBAClE,UAAU,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI;gBAC9D,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;gBACnE,MAAM,EAAE,IAAI;aACb,CAAC,CAAC;YAEH,IAAI,QAAQ,GAAG,EAAE,CAAC;YAClB,IAAI,aAAa,GAA2D,EAAE,CAAC;YAE/E,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBACjC,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;gBAEtC,IAAI,KAAK,EAAE,OAAO,EAAE,CAAC;oBACnB,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC;oBAC1B,MAAM,KAAK,CAAC,OAAO,CAAC;gBACtB,CAAC;gBAED,IAAI,KAAK,EAAE,UAAU,EAAE,CAAC;oBACtB,KAAK,MAAM,QAAQ,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;wBACxC,IAAI,QAAQ,CAAC,QAAQ,EAAE,IAAI,IAAI,QAAQ,CAAC,QAAQ,EAAE,SAAS,EAAE,CAAC;4BAC5D,IAAI,CAAC;gCACH,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;gCACrD,aAAa,CAAC,IAAI,CAAC;oCACjB,IAAI,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI;oCAC5B,IAAI;iCACL,CAAC,CAAC;4BACL,CAAC;4BAAC,MAAM,CAAC;gCACP,kCAAkC;4BACpC,CAAC;wBACH,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAO,GAAG,IAAI,CAAC;YAEf,6CAA6C;YAC7C,OAAO,CAAC,gBAAgB,CACtB,QAAQ,EACR,IAAI,CAAC,MAAM,CAAC,KAAK,EACjB,UAAU,EACV,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EACtB,OAAO,CACR,CAAC;YAEF,OAAO;gBACL,IAAI,EAAE,QAAQ;gBACd,aAAa;gBACb,KAAK,EAAE;oBACL,WAAW,EAAE,UAAU;iBACxB;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,uBAAuB;YACvB,OAAO,CAAC,gBAAgB,CACtB,QAAQ,EACR,IAAI,CAAC,MAAM,CAAC,KAAK,EACjB,UAAU,EACV,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EACtB,OAAO,CACR,CAAC;YAEF,OAAO,CAAC,WAAW,CACjB,sBAAsB,EACtB,eAAe,CAAC,KAAK,CAAC,EACtB,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAC7B,CAAC;YAEF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,kBAAkB;QAChB,OAAO;YACL,QAAQ;YACR,aAAa;YACb,aAAa;YACb,OAAO;YACP,eAAe;YACf,mBAAmB;SACpB,CAAC;IACJ,CAAC;IAES,YAAY,CAAC,KAA4B;QACjD,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACxB,IAAI,EAAE,UAAU;YAChB,QAAQ,EAAE;gBACR,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,EAAE;gBACrB,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,EAAE;gBACnC,UAAU,EAAE,IAAI,CAAC,UAAqC,IAAI,EAAE;aAC7D;SACF,CAAC,CAAC,CAAC;IACN,CAAC;IAES,eAAe,CAAC,UAAsC;QAC9D,MAAM,MAAM,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACrC,MAAM,OAAO,GAAG,MAAM,EAAE,OAAO,CAAC;QAEhC,MAAM,aAAa,GAA2D,EAAE,CAAC;QAEjF,IAAI,OAAO,EAAE,UAAU,EAAE,CAAC;YACxB,KAAK,MAAM,QAAQ,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;gBAC1C,IAAI,QAAQ,CAAC,QAAQ,EAAE,IAAI,IAAI,QAAQ,CAAC,QAAQ,EAAE,SAAS,EAAE,CAAC;oBAC5D,IAAI,CAAC;wBACH,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;wBACrD,aAAa,CAAC,IAAI,CAAC;4BACjB,IAAI,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI;4BAC5B,IAAI;yBACL,CAAC,CAAC;oBACL,CAAC;oBAAC,MAAM,CAAC;wBACP,kCAAkC;oBACpC,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO;YACL,IAAI,EAAE,OAAO,EAAE,OAAO,IAAI,EAAE;YAC5B,aAAa;YACb,KAAK,EAAE;gBACL,YAAY,EAAE,UAAU,CAAC,KAAK,EAAE,aAAa;gBAC7C,gBAAgB,EAAE,UAAU,CAAC,KAAK,EAAE,iBAAiB;gBACrD,WAAW,EAAE,UAAU,CAAC,KAAK,EAAE,YAAY;aAC5C;YACD,YAAY,EAAE,MAAM,EAAE,aAAa,IAAI,SAAS;SACjD,CAAC;IACJ,CAAC;CACF"}