/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import Anthropic from '@anthropic-ai/sdk';
import { BaseLLMProvider } from './base.js';
import { metrics } from '../telemetry/metrics.js';
import { getErrorMessage } from '../utils/errors.js';
export class Anthropic<PERSON>rovider extends BaseLLMProvider {
    client;
    constructor(config) {
        super('Anthropic Claude', config);
        this.validateConfig();
        this.client = new Anthropic({
            apiKey: config.apiKey,
            baseURL: config.baseUrl,
        });
    }
    async generateResponse(message, options = {}) {
        const startTime = Date.now();
        let success = false;
        let tokensUsed = 0;
        try {
            const messages = [
                {
                    role: 'user',
                    content: message,
                },
            ];
            const response = await this.client.messages.create({
                model: this.config.model,
                messages,
                max_tokens: options.maxTokens || this.config.maxTokens || 4096,
                temperature: options.temperature || this.config.temperature || 0.7,
                system: options.systemPrompt,
                tools: options.tools ? this.convertTools(options.tools) : undefined,
            });
            success = true;
            tokensUsed = response.usage.input_tokens + response.usage.output_tokens;
            // Record metrics
            metrics.recordLLMRequest('anthropic', this.config.model, tokensUsed, Date.now() - startTime, success);
            return this.convertResponse(response);
        }
        catch (error) {
            // Record error metrics
            metrics.recordLLMRequest('anthropic', this.config.model, tokensUsed, Date.now() - startTime, success);
            metrics.recordError('anthropic_request_failed', getErrorMessage(error), { model: this.config.model });
            throw error;
        }
    }
    async generateConversationResponse(messages, options = {}) {
        const startTime = Date.now();
        let success = false;
        let tokensUsed = 0;
        try {
            const anthropicMessages = [];
            for (const msg of messages) {
                if (msg.role !== 'system') {
                    anthropicMessages.push({
                        role: msg.role === 'assistant' ? 'assistant' : 'user',
                        content: msg.content,
                    });
                }
            }
            // Extract system message if present
            const systemMessage = messages.find(msg => msg.role === 'system');
            const systemPrompt = systemMessage?.content || options.systemPrompt;
            const response = await this.client.messages.create({
                model: this.config.model,
                messages: anthropicMessages,
                max_tokens: options.maxTokens || this.config.maxTokens || 4096,
                temperature: options.temperature || this.config.temperature || 0.7,
                system: systemPrompt,
                tools: options.tools ? this.convertTools(options.tools) : undefined,
            });
            success = true;
            tokensUsed = response.usage.input_tokens + response.usage.output_tokens;
            // Record metrics
            metrics.recordLLMRequest('anthropic', this.config.model, tokensUsed, Date.now() - startTime, success);
            return this.convertResponse(response);
        }
        catch (error) {
            // Record error metrics
            metrics.recordLLMRequest('anthropic', this.config.model, tokensUsed, Date.now() - startTime, success);
            metrics.recordError('anthropic_conversation_failed', getErrorMessage(error), { model: this.config.model });
            throw error;
        }
    }
    async *generateStreamingResponse(message, options = {}) {
        const startTime = Date.now();
        let success = false;
        let tokensUsed = 0;
        try {
            const messages = [
                {
                    role: 'user',
                    content: message,
                },
            ];
            const stream = await this.client.messages.create({
                model: this.config.model,
                messages,
                max_tokens: options.maxTokens || this.config.maxTokens || 4096,
                temperature: options.temperature || this.config.temperature || 0.7,
                system: options.systemPrompt,
                tools: options.tools ? this.convertTools(options.tools) : undefined,
                stream: true,
            });
            let fullText = '';
            let functionCalls = [];
            let usage = { input_tokens: 0, output_tokens: 0 };
            for await (const chunk of stream) {
                if (chunk.type === 'content_block_delta' && chunk.delta.type === 'text_delta') {
                    const text = chunk.delta.text;
                    fullText += text;
                    yield text;
                }
                else if (chunk.type === 'content_block_start' && chunk.content_block.type === 'tool_use') {
                    const toolUse = chunk.content_block;
                    functionCalls.push({
                        name: toolUse.name,
                        args: toolUse.input,
                    });
                }
                else if (chunk.type === 'message_delta' && chunk.usage) {
                    usage.output_tokens = chunk.usage.output_tokens || 0;
                }
                else if (chunk.type === 'message_start' && chunk.message.usage) {
                    usage.input_tokens = chunk.message.usage.input_tokens;
                }
            }
            success = true;
            tokensUsed = usage.input_tokens + usage.output_tokens;
            // Record metrics
            metrics.recordLLMRequest('anthropic', this.config.model, tokensUsed, Date.now() - startTime, success);
            return {
                text: fullText,
                functionCalls,
                usage: {
                    promptTokens: usage.input_tokens,
                    completionTokens: usage.output_tokens,
                    totalTokens: tokensUsed,
                },
            };
        }
        catch (error) {
            // Record error metrics
            metrics.recordLLMRequest('anthropic', this.config.model, tokensUsed, Date.now() - startTime, success);
            metrics.recordError('anthropic_stream_failed', getErrorMessage(error), { model: this.config.model });
            throw error;
        }
    }
    getAvailableModels() {
        return [
            'claude-3-5-sonnet-20241022',
            'claude-3-5-haiku-20241022',
            'claude-3-opus-20240229',
            'claude-3-sonnet-20240229',
            'claude-3-haiku-20240307',
        ];
    }
    convertTools(tools) {
        return tools.map(tool => ({
            name: tool.name || '',
            description: tool.description || '',
            input_schema: {
                type: 'object',
                properties: tool.parameters?.properties || {},
                required: tool.parameters?.required || [],
            },
        }));
    }
    convertResponse(response) {
        let text = '';
        const functionCalls = [];
        for (const content of response.content) {
            if (content.type === 'text') {
                text += content.text;
            }
            else if (content.type === 'tool_use') {
                functionCalls.push({
                    name: content.name,
                    args: content.input,
                });
            }
        }
        return {
            text,
            functionCalls,
            usage: {
                promptTokens: response.usage.input_tokens,
                completionTokens: response.usage.output_tokens,
                totalTokens: response.usage.input_tokens + response.usage.output_tokens,
            },
            finishReason: response.stop_reason || undefined,
        };
    }
}
//# sourceMappingURL=anthropic.js.map