/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
export interface MetricEvent {
    name: string;
    timestamp: number;
    properties?: Record<string, unknown>;
    measurements?: Record<string, number>;
}
export interface SessionMetrics {
    sessionId: string;
    startTime: number;
    endTime?: number;
    events: MetricEvent[];
    totalTokensUsed: number;
    totalRequests: number;
    errors: number;
}
export declare class MetricsCollector {
    private static instance;
    private currentSession;
    private enabled;
    private constructor();
    static getInstance(): MetricsCollector;
    /**
     * Starts a new metrics session
     */
    startSession(sessionId: string): void;
    /**
     * Ends the current session
     */
    endSession(): void;
    /**
     * Records a metric event
     */
    recordEvent(name: string, properties?: Record<string, unknown>, measurements?: Record<string, number>): void;
    /**
     * Records LLM request metrics
     */
    recordLLMRequest(provider: string, model: string, tokensUsed: number, duration: number, success: boolean): void;
    /**
     * Records tool execution metrics
     */
    recordToolExecution(toolName: string, duration: number, success: boolean, errorMessage?: string): void;
    /**
     * Records file operation metrics
     */
    recordFileOperation(operation: string, fileCount: number, duration: number, success: boolean): void;
    /**
     * Records error metrics
     */
    recordError(errorType: string, errorMessage: string, context?: Record<string, unknown>): void;
    /**
     * Gets current session metrics
     */
    getCurrentSession(): SessionMetrics | null;
    /**
     * Enables or disables metrics collection
     */
    setEnabled(enabled: boolean): void;
    /**
     * Checks if metrics collection is enabled
     */
    isEnabled(): boolean;
}
export declare const metrics: MetricsCollector;
export declare enum FileOperation {
    READ = "read",
    WRITE = "write",
    DELETE = "delete",
    COPY = "copy",
    MOVE = "move"
}
export declare function recordFileOperationMetric(operation: FileOperation, fileCount: number, duration: number, success: boolean): void;
//# sourceMappingURL=metrics.d.ts.map