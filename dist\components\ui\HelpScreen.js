import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Box, Text, useInput } from 'ink';
export const HelpScreen = ({ themeManager, onClose, }) => {
    useInput((input, key) => {
        if (key.escape || key.return || input === 'q' || input === 'Q') {
            onClose();
        }
    });
    return (_jsxs(Box, { flexDirection: "column", borderStyle: "round", borderColor: "blue", padding: 1, children: [_jsx(Text, { children: themeManager.primary('🔧 Arien AI CLI Help') }), _jsx(Text, {}), _jsx(Text, { children: themeManager.secondary('Keyboard Shortcuts:') }), _jsx(Text, { children: themeManager.muted('  Enter       - Send message') }), _jsx(Text, { children: themeManager.muted('  Ctrl+H      - Show this help') }), _jsx(Text, { children: themeManager.muted('  Ctrl+L      - Clear chat history') }), _jsx(Text, { children: themeManager.muted('  Ctrl+C      - Exit application') }), _jsx(Text, {}), _jsx(Text, { children: themeManager.secondary('Features:') }), _jsx(Text, { children: themeManager.muted('  • Multi-provider AI support (Google, OpenAI, DeepSeek, Anthropic)') }), _jsx(Text, { children: themeManager.muted('  • Function calling with built-in tools') }), _jsx(Text, { children: themeManager.muted('  • Real-time streaming responses') }), _jsx(Text, { children: themeManager.muted('  • File operations and shell commands') }), _jsx(Text, { children: themeManager.muted('  • MCP (Model Context Protocol) support') }), _jsx(Text, {}), _jsx(Text, { children: themeManager.secondary('Available Tools:') }), _jsx(Text, { children: themeManager.muted('  • File operations (read, write, edit)') }), _jsx(Text, { children: themeManager.muted('  • Shell command execution') }), _jsx(Text, { children: themeManager.muted('  • Web search and fetch') }), _jsx(Text, { children: themeManager.muted('  • Memory and context management') }), _jsx(Text, { children: themeManager.muted('  • Code analysis and modification') }), _jsx(Text, {}), _jsx(Text, { children: themeManager.secondary('Examples:') }), _jsx(Text, { children: themeManager.muted('  "Read the package.json file"') }), _jsx(Text, { children: themeManager.muted('  "Search for React components in src/"') }), _jsx(Text, { children: themeManager.muted('  "Create a new TypeScript file with a class"') }), _jsx(Text, { children: themeManager.muted('  "Run npm install and show the output"') }), _jsx(Text, {}), _jsx(Text, { children: themeManager.muted('Press Enter, Esc, or Q to close help') })] }));
};
//# sourceMappingURL=HelpScreen.js.map