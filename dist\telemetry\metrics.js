/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
export class MetricsCollector {
    static instance = null;
    currentSession = null;
    enabled = true;
    constructor() { }
    static getInstance() {
        if (!MetricsCollector.instance) {
            MetricsCollector.instance = new MetricsCollector();
        }
        return MetricsCollector.instance;
    }
    /**
     * Starts a new metrics session
     */
    startSession(sessionId) {
        if (!this.enabled)
            return;
        this.currentSession = {
            sessionId,
            startTime: Date.now(),
            events: [],
            totalTokensUsed: 0,
            totalRequests: 0,
            errors: 0,
        };
        this.recordEvent('session_started', {
            sessionId,
        });
    }
    /**
     * Ends the current session
     */
    endSession() {
        if (!this.enabled || !this.currentSession)
            return;
        this.currentSession.endTime = Date.now();
        this.recordEvent('session_ended', {
            sessionId: this.currentSession.sessionId,
            duration: this.currentSession.endTime - this.currentSession.startTime,
            totalTokensUsed: this.currentSession.totalTokensUsed,
            totalRequests: this.currentSession.totalRequests,
            errors: this.currentSession.errors,
        });
        // In a full implementation, this would send metrics to a telemetry service
        this.currentSession = null;
    }
    /**
     * Records a metric event
     */
    recordEvent(name, properties, measurements) {
        if (!this.enabled)
            return;
        const event = {
            name,
            timestamp: Date.now(),
            properties,
            measurements,
        };
        if (this.currentSession) {
            this.currentSession.events.push(event);
        }
        // Log to console in debug mode
        if (process.env.DEBUG_METRICS) {
            console.debug('Metric event:', event);
        }
    }
    /**
     * Records LLM request metrics
     */
    recordLLMRequest(provider, model, tokensUsed, duration, success) {
        if (!this.enabled)
            return;
        if (this.currentSession) {
            this.currentSession.totalRequests++;
            this.currentSession.totalTokensUsed += tokensUsed;
            if (!success) {
                this.currentSession.errors++;
            }
        }
        this.recordEvent('llm_request', {
            provider,
            model,
            success,
        }, {
            tokensUsed,
            duration,
        });
    }
    /**
     * Records tool execution metrics
     */
    recordToolExecution(toolName, duration, success, errorMessage) {
        if (!this.enabled)
            return;
        this.recordEvent('tool_execution', {
            toolName,
            success,
            errorMessage,
        }, {
            duration,
        });
    }
    /**
     * Records file operation metrics
     */
    recordFileOperation(operation, fileCount, duration, success) {
        if (!this.enabled)
            return;
        this.recordEvent('file_operation', {
            operation,
            success,
        }, {
            fileCount,
            duration,
        });
    }
    /**
     * Records error metrics
     */
    recordError(errorType, errorMessage, context) {
        if (!this.enabled)
            return;
        if (this.currentSession) {
            this.currentSession.errors++;
        }
        this.recordEvent('error', {
            errorType,
            errorMessage,
            ...context,
        });
    }
    /**
     * Gets current session metrics
     */
    getCurrentSession() {
        return this.currentSession;
    }
    /**
     * Enables or disables metrics collection
     */
    setEnabled(enabled) {
        this.enabled = enabled;
    }
    /**
     * Checks if metrics collection is enabled
     */
    isEnabled() {
        return this.enabled;
    }
}
// Export singleton instance
export const metrics = MetricsCollector.getInstance();
// Export types and helper functions
export var FileOperation;
(function (FileOperation) {
    FileOperation["READ"] = "read";
    FileOperation["WRITE"] = "write";
    FileOperation["CREATE"] = "create";
    FileOperation["UPDATE"] = "update";
    FileOperation["DELETE"] = "delete";
    FileOperation["COPY"] = "copy";
    FileOperation["MOVE"] = "move";
})(FileOperation || (FileOperation = {}));
export function recordFileOperationMetric(
// eslint-disable-next-line @typescript-eslint/no-unused-vars
config, operation, lines, mimetype, extension) {
    // Record the operation with additional metadata
    metrics.recordFileOperation(operation, lines, 0, true);
    // Record additional metadata as a separate event
    metrics.recordEvent('file_operation_details', {
        operation,
        mimetype,
        extension,
    }, {
        lines,
    });
}
//# sourceMappingURL=metrics.js.map