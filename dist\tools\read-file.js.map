{"version": 3, "file": "read-file.js", "sourceRoot": "", "sources": ["../../src/tools/read-file.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,EAAE,eAAe,EAAE,MAAM,6BAA6B,CAAC;AAC9D,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAC9D,OAAO,EAAE,QAAQ,EAAc,MAAM,YAAY,CAAC;AAClD,OAAO,EACL,YAAY,EACZ,wBAAwB,GACzB,MAAM,uBAAuB,CAAC;AAC/B,OAAO,EAAE,MAAM,IAAI,CAAC;AAEpB,OAAO,EACL,yBAAyB,EACzB,aAAa,GACd,MAAM,yBAAyB,CAAC;AAwBjC;;GAEG;AACH,MAAM,OAAO,YAAa,SAAQ,QAAwC;IAI9D;IACA;IAJV,MAAM,CAAU,IAAI,GAAW,WAAW,CAAC;IAE3C,YACU,aAAqB,EACrB,MAAc;QAEtB,KAAK,CACH,YAAY,CAAC,IAAI,EACjB,UAAU,EACV,qMAAqM,EACrM;YACE,UAAU,EAAE;gBACV,aAAa,EAAE;oBACb,WAAW,EACT,mJAAmJ;oBACrJ,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,IAAI;iBACd;gBACD,MAAM,EAAE;oBACN,WAAW,EACT,8IAA8I;oBAChJ,IAAI,EAAE,QAAQ;iBACf;gBACD,KAAK,EAAE;oBACL,WAAW,EACT,uLAAuL;oBACzL,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,QAAQ,EAAE,CAAC,eAAe,CAAC;YAC3B,IAAI,EAAE,QAAQ;SACf,CACF,CAAC;QA7BM,kBAAa,GAAb,aAAa,CAAQ;QACrB,WAAM,GAAN,MAAM,CAAQ;QA6BtB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;IACnD,CAAC;IAED,kBAAkB,CAAC,MAA0B;QAC3C,IACE,IAAI,CAAC,MAAM,CAAC,UAAU;YACtB,CAAC,eAAe,CAAC,QAAQ,CACvB,IAAI,CAAC,MAAM,CAAC,UAAqC,EACjD,MAAM,CACP,EACD,CAAC;YACD,OAAO,sCAAsC,CAAC;QAChD,CAAC;QACD,MAAM,QAAQ,GAAG,MAAM,CAAC,aAAa,CAAC;QACtC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC/B,OAAO,iDAAiD,QAAQ,sCAAsC,CAAC;QACzG,CAAC;QACD,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;YAChD,OAAO,gDAAgD,IAAI,CAAC,aAAa,MAAM,QAAQ,EAAE,CAAC;QAC5F,CAAC;QACD,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrD,OAAO,sCAAsC,CAAC;QAChD,CAAC;QACD,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC;YACpD,OAAO,iCAAiC,CAAC;QAC3C,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;QACjD,IAAI,WAAW,CAAC,mBAAmB,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC;YAC1D,MAAM,YAAY,GAAG,YAAY,CAC/B,MAAM,CAAC,aAAa,EACpB,IAAI,CAAC,aAAa,CACnB,CAAC;YACF,OAAO,cAAc,WAAW,CAAC,YAAY,CAAC,2CAA2C,CAAC;QAC5F,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,cAAc,CAAC,MAA0B;QACvC,IACE,CAAC,MAAM;YACP,OAAO,MAAM,CAAC,aAAa,KAAK,QAAQ;YACxC,MAAM,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE,EAClC,CAAC;YACD,OAAO,kBAAkB,CAAC;QAC5B,CAAC;QACD,MAAM,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC5E,OAAO,WAAW,CAAC,YAAY,CAAC,CAAC;IACnC,CAAC;IAED,KAAK,CAAC,OAAO,CACX,MAA0B,EAC1B,OAAoB;QAEpB,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QACxD,IAAI,eAAe,EAAE,CAAC;YACpB,OAAO;gBACL,UAAU,EAAE,+CAA+C,eAAe,EAAE;gBAC5E,aAAa,EAAE,eAAe;aAC/B,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,8BAA8B;YAC9B,MAAM,WAAW,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YAErE,4CAA4C;YAC5C,MAAM,MAAM,GAAG,wBAAwB,CACrC,MAAM,CAAC,aAAa,EACpB,WAAW,EACX;gBACE,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,KAAK,EAAE,MAAM,CAAC,KAAK;aACpB,CACF,CAAC;YAEF,yBAAyB,CACvB,IAAI,CAAC,MAAM,EACX,aAAa,CAAC,IAAI,EAClB,CAAC,EACD,YAAY,EACZ,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,CACnC,CAAC;YAEF,OAAO;gBACL,UAAU,EAAE,MAAM;gBAClB,aAAa,EAAE,cAAc,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE;aACnE,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,sBAAsB,MAAM,CAAC,aAAa,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;YAC7H,yBAAyB,CACvB,IAAI,CAAC,MAAM,EACX,aAAa,CAAC,IAAI,EAClB,CAAC,EACD,YAAY,EACZ,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,CACnC,CAAC;YAEF,OAAO;gBACL,UAAU,EAAE,YAAY;gBACxB,aAAa,EAAE,YAAY;aAC5B,CAAC;QACJ,CAAC;IACH,CAAC"}