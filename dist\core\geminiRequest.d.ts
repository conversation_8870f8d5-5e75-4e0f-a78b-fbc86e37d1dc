/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { GenerateContentParameters, FunctionDeclaration } from '@google/genai';
import { GeminiClient } from './client.js';
export interface GeminiRequestOptions {
    systemPrompt?: string;
    tools?: FunctionDeclaration[];
    temperature?: number;
    maxTokens?: number;
}
export interface GeminiResponse {
    text: string;
    functionCalls: Array<{
        name: string;
        args: Record<string, unknown>;
    }>;
    usage?: {
        promptTokens?: number;
        completionTokens?: number;
        totalTokens?: number;
    };
}
/**
 * Makes a request to the Gemini API with proper formatting
 */
export declare function makeGeminiRequest(client: GeminiClient, userMessage: string, options?: GeminiRequestOptions): Promise<GeminiResponse>;
/**
 * Makes a streaming request to the Gemini API
 */
export declare function makeGeminiStreamRequest(client: GeminiClient, userMessage: string, options?: GeminiRequestOptions): AsyncGenerator<string, GeminiResponse, unknown>;
/**
 * Builds a conversation request with history
 */
export declare function buildConversationRequest(messages: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
}>, options?: GeminiRequestOptions, modelName?: string): GenerateContentParameters;
//# sourceMappingURL=geminiRequest.d.ts.map