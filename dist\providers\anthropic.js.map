{"version": 3, "file": "anthropic.js", "sourceRoot": "", "sources": ["../../src/providers/anthropic.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,SAAS,MAAM,mBAAmB,CAAC;AAE1C,OAAO,EAAE,eAAe,EAA8D,MAAM,WAAW,CAAC;AACxG,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAErD,MAAM,OAAO,iBAAkB,SAAQ,eAAe;IAC5C,MAAM,CAAY;IAE1B,YAAY,MAAsB;QAChC,KAAK,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;QAClC,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,IAAI,CAAC,MAAM,GAAG,IAAI,SAAS,CAAC;YAC1B,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,OAAO,EAAE,MAAM,CAAC,OAAO;SACxB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,gBAAgB,CACpB,OAAe,EACf,UAA6B,EAAE;QAE/B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,IAAI,CAAC;YACH,MAAM,QAAQ,GAA6B;gBACzC;oBACE,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,OAAO;iBACjB;aACF,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;gBACjD,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;gBACxB,QAAQ;gBACR,UAAU,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI;gBAC9D,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,GAAG;gBAClE,MAAM,EAAE,OAAO,CAAC,YAAY;gBAC5B,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;aACpE,CAAC,CAAC;YAEH,OAAO,GAAG,IAAI,CAAC;YACf,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC,YAAY,GAAG,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC;YAExE,iBAAiB;YACjB,OAAO,CAAC,gBAAgB,CACtB,WAAW,EACX,IAAI,CAAC,MAAM,CAAC,KAAK,EACjB,UAAU,EACV,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EACtB,OAAO,CACR,CAAC;YAEF,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QACxC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,uBAAuB;YACvB,OAAO,CAAC,gBAAgB,CACtB,WAAW,EACX,IAAI,CAAC,MAAM,CAAC,KAAK,EACjB,UAAU,EACV,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EACtB,OAAO,CACR,CAAC;YAEF,OAAO,CAAC,WAAW,CACjB,0BAA0B,EAC1B,eAAe,CAAC,KAAK,CAAC,EACtB,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAC7B,CAAC;YAEF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,4BAA4B,CAChC,QAAsB,EACtB,UAA6B,EAAE;QAE/B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,IAAI,CAAC;YACH,MAAM,iBAAiB,GAA6B,EAAE,CAAC;YAEvD,KAAK,MAAM,GAAG,IAAI,QAAQ,EAAE,CAAC;gBAC3B,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBAC1B,iBAAiB,CAAC,IAAI,CAAC;wBACrB,IAAI,EAAE,GAAG,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM;wBACrD,OAAO,EAAE,GAAG,CAAC,OAAO;qBACrB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,oCAAoC;YACpC,MAAM,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;YAClE,MAAM,YAAY,GAAG,aAAa,EAAE,OAAO,IAAI,OAAO,CAAC,YAAY,CAAC;YAEpE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;gBACjD,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;gBACxB,QAAQ,EAAE,iBAAiB;gBAC3B,UAAU,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI;gBAC9D,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,GAAG;gBAClE,MAAM,EAAE,YAAY;gBACpB,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;aACpE,CAAC,CAAC;YAEH,OAAO,GAAG,IAAI,CAAC;YACf,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC,YAAY,GAAG,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC;YAExE,iBAAiB;YACjB,OAAO,CAAC,gBAAgB,CACtB,WAAW,EACX,IAAI,CAAC,MAAM,CAAC,KAAK,EACjB,UAAU,EACV,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EACtB,OAAO,CACR,CAAC;YAEF,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QACxC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,uBAAuB;YACvB,OAAO,CAAC,gBAAgB,CACtB,WAAW,EACX,IAAI,CAAC,MAAM,CAAC,KAAK,EACjB,UAAU,EACV,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EACtB,OAAO,CACR,CAAC;YAEF,OAAO,CAAC,WAAW,CACjB,+BAA+B,EAC/B,eAAe,CAAC,KAAK,CAAC,EACtB,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAC7B,CAAC;YAEF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAA,CAAE,yBAAyB,CAC9B,OAAe,EACf,UAA6B,EAAE;QAE/B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,IAAI,CAAC;YACH,MAAM,QAAQ,GAA6B;gBACzC;oBACE,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,OAAO;iBACjB;aACF,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAC/C,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;gBACxB,QAAQ;gBACR,UAAU,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI;gBAC9D,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,GAAG;gBAClE,MAAM,EAAE,OAAO,CAAC,YAAY;gBAC5B,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;gBACnE,MAAM,EAAE,IAAI;aACb,CAAC,CAAC;YAEH,IAAI,QAAQ,GAAG,EAAE,CAAC;YAClB,IAAI,aAAa,GAA2D,EAAE,CAAC;YAC/E,IAAI,KAAK,GAAoD,EAAE,YAAY,EAAE,CAAC,EAAE,aAAa,EAAE,CAAC,EAAE,CAAC;YAEnG,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBACjC,IAAI,KAAK,CAAC,IAAI,KAAK,qBAAqB,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;oBAC9E,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC;oBAC9B,QAAQ,IAAI,IAAI,CAAC;oBACjB,MAAM,IAAI,CAAC;gBACb,CAAC;qBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,qBAAqB,IAAI,KAAK,CAAC,aAAa,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;oBAC3F,MAAM,OAAO,GAAG,KAAK,CAAC,aAAa,CAAC;oBACpC,aAAa,CAAC,IAAI,CAAC;wBACjB,IAAI,EAAE,OAAO,CAAC,IAAI;wBAClB,IAAI,EAAE,OAAO,CAAC,KAAgC;qBAC/C,CAAC,CAAC;gBACL,CAAC;qBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,eAAe,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;oBACzD,KAAK,CAAC,aAAa,GAAG,KAAK,CAAC,KAAK,CAAC,aAAa,IAAI,CAAC,CAAC;gBACvD,CAAC;qBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,eAAe,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;oBACjE,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC;gBACxD,CAAC;YACH,CAAC;YAED,OAAO,GAAG,IAAI,CAAC;YACf,UAAU,GAAG,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC,aAAa,CAAC;YAEtD,iBAAiB;YACjB,OAAO,CAAC,gBAAgB,CACtB,WAAW,EACX,IAAI,CAAC,MAAM,CAAC,KAAK,EACjB,UAAU,EACV,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EACtB,OAAO,CACR,CAAC;YAEF,OAAO;gBACL,IAAI,EAAE,QAAQ;gBACd,aAAa;gBACb,KAAK,EAAE;oBACL,YAAY,EAAE,KAAK,CAAC,YAAY;oBAChC,gBAAgB,EAAE,KAAK,CAAC,aAAa;oBACrC,WAAW,EAAE,UAAU;iBACxB;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,uBAAuB;YACvB,OAAO,CAAC,gBAAgB,CACtB,WAAW,EACX,IAAI,CAAC,MAAM,CAAC,KAAK,EACjB,UAAU,EACV,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EACtB,OAAO,CACR,CAAC;YAEF,OAAO,CAAC,WAAW,CACjB,yBAAyB,EACzB,eAAe,CAAC,KAAK,CAAC,EACtB,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAC7B,CAAC;YAEF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,kBAAkB;QAChB,OAAO;YACL,4BAA4B;YAC5B,2BAA2B;YAC3B,wBAAwB;YACxB,0BAA0B;YAC1B,yBAAyB;SAC1B,CAAC;IACJ,CAAC;IAES,YAAY,CAAC,KAA4B;QACjD,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACxB,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,EAAE;YACrB,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,EAAE;YACnC,YAAY,EAAE;gBACZ,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAG,IAAI,CAAC,UAAkB,EAAE,UAAU,IAAI,EAAE;gBACtD,QAAQ,EAAG,IAAI,CAAC,UAAkB,EAAE,QAAQ,IAAI,EAAE;aACnD;SACF,CAAC,CAAC,CAAC;IACN,CAAC;IAES,eAAe,CAAC,QAA2B;QACnD,IAAI,IAAI,GAAG,EAAE,CAAC;QACd,MAAM,aAAa,GAA2D,EAAE,CAAC;QAEjF,KAAK,MAAM,OAAO,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;YACvC,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;gBAC5B,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC;YACvB,CAAC;iBAAM,IAAI,OAAO,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;gBACvC,aAAa,CAAC,IAAI,CAAC;oBACjB,IAAI,EAAE,OAAO,CAAC,IAAI;oBAClB,IAAI,EAAE,OAAO,CAAC,KAAgC;iBAC/C,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO;YACL,IAAI;YACJ,aAAa;YACb,KAAK,EAAE;gBACL,YAAY,EAAE,QAAQ,CAAC,KAAK,CAAC,YAAY;gBACzC,gBAAgB,EAAE,QAAQ,CAAC,KAAK,CAAC,aAAa;gBAC9C,WAAW,EAAE,QAAQ,CAAC,KAAK,CAAC,YAAY,GAAG,QAAQ,CAAC,KAAK,CAAC,aAAa;aACxE;YACD,YAAY,EAAE,QAAQ,CAAC,WAAW,IAAI,SAAS;SAChD,CAAC;IACJ,CAAC;CACF"}