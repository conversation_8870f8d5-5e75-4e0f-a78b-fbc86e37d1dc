/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { GenerateContentParameters, Content, FunctionDeclaration } from '@google/genai';
import { GeminiClient } from './client.js';
import { getResponseText, getFunctionCalls } from '../utils/generateContentResponseUtilities.js';
import { metrics } from '../telemetry/metrics.js';

export interface GeminiRequestOptions {
  systemPrompt?: string;
  tools?: FunctionDeclaration[];
  temperature?: number;
  maxTokens?: number;
}

export interface GeminiResponse {
  text: string;
  functionCalls: Array<{
    name: string;
    args: Record<string, unknown>;
  }>;
  usage?: {
    promptTokens?: number;
    completionTokens?: number;
    totalTokens?: number;
  };
}

/**
 * Makes a request to the Gemini API with proper formatting
 */
export async function makeGeminiRequest(
  client: GeminiClient,
  userMessage: string,
  options: GeminiRequestOptions = {}
): Promise<GeminiResponse> {
  const { systemPrompt, tools, temperature, maxTokens } = options;

  // Build the request
  const contents: Content[] = [];

  // Add system prompt if provided
  if (systemPrompt) {
    contents.push({ parts: [{ text: systemPrompt }], role: 'user' });
  }

  // Add user message
  contents.push({ parts: [{ text: userMessage }], role: 'user' });

  const request: GenerateContentParameters = {
    model: client.getModelName(),
    contents,
    config: {
      tools: tools ? [{ functionDeclarations: tools }] : undefined,
      temperature: temperature || 0.7,
      maxOutputTokens: maxTokens || 8192,
    },
  };

  try {
    const response = await client.generateContent(request);
    
    const text = getResponseText(response);
    const functionCalls = getFunctionCalls(response);
    
    return {
      text,
      functionCalls,
      usage: {
        promptTokens: response.usageMetadata?.promptTokenCount,
        completionTokens: response.usageMetadata?.candidatesTokenCount,
        totalTokens: response.usageMetadata?.totalTokenCount,
      },
    };
  } catch (error) {
    metrics.recordError(
      'gemini_request_error',
      error instanceof Error ? error.message : String(error),
      { userMessage: userMessage.substring(0, 100) }
    );
    throw error;
  }
}

/**
 * Makes a streaming request to the Gemini API
 */
export async function* makeGeminiStreamRequest(
  client: GeminiClient,
  userMessage: string,
  options: GeminiRequestOptions = {}
): AsyncGenerator<string, GeminiResponse, unknown> {
  const { systemPrompt, tools, temperature, maxTokens } = options;

  // Build the request
  const contents: Content[] = [];

  // Add system prompt if provided
  if (systemPrompt) {
    contents.push({ parts: [{ text: systemPrompt }], role: 'user' });
  }

  // Add user message
  contents.push({ parts: [{ text: userMessage }], role: 'user' });

  const request: GenerateContentParameters = {
    model: client.getModelName(),
    contents,
    config: {
      tools: tools ? [{ functionDeclarations: tools }] : undefined,
      temperature: temperature || 0.7,
      maxOutputTokens: maxTokens || 8192,
    },
  };

  try {
    const stream = await client.generateContentStream(request);
    
    let fullText = '';
    let functionCalls: Array<{ name: string; args: Record<string, unknown> }> = [];
    let usage: GeminiResponse['usage'] = {};

    for await (const chunk of stream) {
      const chunkText = getResponseText(chunk);
      const chunkFunctionCalls = getFunctionCalls(chunk);
      
      if (chunkText) {
        fullText += chunkText;
        yield chunkText;
      }
      
      if (chunkFunctionCalls.length > 0) {
        functionCalls.push(...chunkFunctionCalls);
      }
      
      if (chunk.usageMetadata) {
        usage = {
          promptTokens: chunk.usageMetadata.promptTokenCount,
          completionTokens: chunk.usageMetadata.candidatesTokenCount,
          totalTokens: chunk.usageMetadata.totalTokenCount,
        };
      }
    }

    return {
      text: fullText,
      functionCalls,
      usage,
    };
  } catch (error) {
    metrics.recordError(
      'gemini_stream_error',
      error instanceof Error ? error.message : String(error),
      { userMessage: userMessage.substring(0, 100) }
    );
    throw error;
  }
}

/**
 * Builds a conversation request with history
 */
export function buildConversationRequest(
  messages: Array<{ role: 'user' | 'assistant' | 'system'; content: string }>,
  options: GeminiRequestOptions = {},
  modelName: string = 'gemini-1.5-pro'
): GenerateContentParameters {
  const { tools, temperature, maxTokens } = options;

  const contents: Content[] = [];

  for (const message of messages) {
    if (message.role === 'system') {
      // System messages are typically added as the first user message
      contents.push({ parts: [{ text: `System: ${message.content}` }], role: 'user' });
    } else if (message.role === 'user') {
      contents.push({ parts: [{ text: message.content }], role: 'user' });
    } else if (message.role === 'assistant') {
      contents.push({ parts: [{ text: message.content }], role: 'model' });
    }
  }

  return {
    model: modelName,
    contents,
    config: {
      tools: tools ? [{ functionDeclarations: tools }] : undefined,
      temperature: temperature || 0.7,
      maxOutputTokens: maxTokens || 8192,
    },
  };
}
