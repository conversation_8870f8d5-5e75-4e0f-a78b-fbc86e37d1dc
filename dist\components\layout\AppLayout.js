import { jsx as _jsx } from "react/jsx-runtime";
import { Box } from 'ink';
import { ErrorBoundary } from '../ui/ErrorBoundary.js';
export const AppLayout = ({ children, themeManager, fullScreen = true, padding = 0, }) => {
    return (_jsx(ErrorBoundary, { themeManager: themeManager, children: _jsx(Box, { flexDirection: "column", width: "100%", height: fullScreen ? "100%" : undefined, padding: padding, children: children }) }));
};
//# sourceMappingURL=AppLayout.js.map