/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import fs from 'fs';
export interface FileFilterOptions {
    respectGitIgnore?: boolean;
    includeHidden?: boolean;
    maxDepth?: number;
}
export declare class FileDiscoveryService {
    private rootPath;
    private gitRoot;
    private gitIgnoreCache;
    private trackedFilesCache;
    constructor(rootPath: string);
    /**
     * Filters files based on git ignore rules
     */
    filterFiles(relativePaths: string[], options?: FileFilterOptions): string[];
    /**
     * Checks if a file should be git-ignored
     */
    shouldGitIgnoreFile(relativePath: string): boolean;
    /**
     * Checks if a file is tracked by git
     */
    private isTrackedFile;
    /**
     * Loads tracked files from git
     */
    private loadTrackedFiles;
    /**
     * Simple git ignore pattern matching
     */
    private matchesGitIgnorePatterns;
    /**
     * Checks if a file is hidden (starts with .)
     */
    private isHiddenFile;
    /**
     * Discovers files in a directory
     */
    discoverFiles(searchPath?: string, options?: FileFilterOptions): Promise<string[]>;
    /**
     * Recursively discovers files
     */
    private discoverFilesRecursive;
    /**
     * Gets file statistics
     */
    getFileStats(filePath: string): Promise<fs.Stats | null>;
    /**
     * Gets git ignore patterns
     */
    getGitIgnorePatterns(): string[];
    /**
     * Clears internal caches
     */
    clearCache(): void;
}
//# sourceMappingURL=fileDiscoveryService.d.ts.map