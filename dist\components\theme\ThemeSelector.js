import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState } from 'react';
import { Box, Text, useInput } from 'ink';
import { Header } from '../ui/Header.js';
export const ThemeSelector = ({ themeManager, onThemeSelected, onSkip, }) => {
    const [selectedIndex, setSelectedIndex] = useState(0);
    const [previewMode, setPreviewMode] = useState(false);
    const themes = themeManager.getAvailableThemes();
    useInput((input, key) => {
        if (!previewMode) {
            if (key.upArrow && selectedIndex > 0) {
                setSelectedIndex(selectedIndex - 1);
            }
            else if (key.downArrow && selectedIndex < themes.length - 1) {
                setSelectedIndex(selectedIndex + 1);
            }
            else if (key.return) {
                setPreviewMode(true);
                // Temporarily apply the selected theme for preview
                themeManager.setTheme(themes[selectedIndex].name);
            }
            else if (input === 's' || input === 'S') {
                onSkip();
            }
        }
        else {
            if (key.return) {
                onThemeSelected(themes[selectedIndex].name);
            }
            else if (key.escape) {
                setPreviewMode(false);
                // Reset to current theme
                themeManager.setTheme(themeManager.getCurrentTheme().name);
            }
            else if (key.upArrow && selectedIndex > 0) {
                setSelectedIndex(selectedIndex - 1);
                themeManager.setTheme(themes[selectedIndex - 1].name);
            }
            else if (key.downArrow && selectedIndex < themes.length - 1) {
                setSelectedIndex(selectedIndex + 1);
                themeManager.setTheme(themes[selectedIndex + 1].name);
            }
        }
    });
    const renderThemeList = () => (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: themeManager.secondary('Available Themes:') }), _jsx(Text, {}), themes.map((themeItem, index) => (_jsx(Box, { marginLeft: 2, children: _jsxs(Text, { children: [index === selectedIndex ? themeManager.highlight(' > ') : '   ', themeManager.primary(themeItem.theme.name), themeManager.muted(` - ${themeItem.theme.description}`)] }) }, themeItem.name))), _jsx(Text, {}), _jsx(Text, { children: themeManager.muted('Use ↑↓ to navigate, Enter to preview, S to skip') })] }));
    const renderPreview = () => {
        const currentTheme = themes[selectedIndex].theme;
        return (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: themeManager.secondary(`Previewing: ${currentTheme.name}`) }), _jsx(Text, {}), _jsxs(Box, { flexDirection: "column", marginLeft: 2, borderStyle: "round", borderColor: "gray", padding: 1, children: [_jsx(Text, { children: themeManager.primary('Primary text example') }), _jsx(Text, { children: themeManager.secondary('Secondary text example') }), _jsx(Text, { children: themeManager.accent('Accent text example') }), _jsx(Text, { children: themeManager.success('✓ Success message') }), _jsx(Text, { children: themeManager.warning('⚠ Warning message') }), _jsx(Text, { children: themeManager.error('✗ Error message') }), _jsx(Text, { children: themeManager.info('ℹ Info message') }), _jsx(Text, { children: themeManager.muted('Muted text example') }), _jsx(Text, { children: themeManager.highlight(' Highlighted text ') }), _jsxs(Box, { marginTop: 1, children: [_jsx(Text, { children: themeManager.primary('Symbols: ') }), _jsxs(Text, { children: [currentTheme.symbols.bullet, " ", currentTheme.symbols.arrow, " ", currentTheme.symbols.check, " ", currentTheme.symbols.cross] })] })] }), _jsx(Text, {}), _jsx(Text, { children: themeManager.muted('Use ↑↓ to try other themes, Enter to select, Esc to go back') })] }));
    };
    return (_jsxs(Box, { flexDirection: "column", padding: 1, children: [_jsx(Header, { themeManager: themeManager, title: "\uD83C\uDFA8 Theme Selection", subtitle: "Choose your preferred CLI theme" }), !previewMode ? renderThemeList() : renderPreview()] }));
};
//# sourceMappingURL=ThemeSelector.js.map